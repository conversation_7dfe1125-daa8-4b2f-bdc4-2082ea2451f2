<template>
  <div class="visual-editor-container">
    <div class="header">
      <h1>🎨 Vue Visual Editor</h1>
      <p>Create visual designs with drag-and-drop elements</p>
    </div>

    <!-- Top Ribbon Menu -->
    <div class="ribbon-toolbar">
      <div class="ribbon-section">
        <div class="ribbon-group">
          <label class="ribbon-label">Actions</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="duplicateSelected" :disabled="selectedElements.length === 0" title="Duplicate (Ctrl+D)">
              <span class="ribbon-icon">📋</span>
              <span class="ribbon-text">Duplicate</span>
            </button>
            <button class="ribbon-btn" @click="rotateSelected" :disabled="selectedElements.length === 0" title="Rotate (R)">
              <span class="ribbon-icon">🔄</span>
              <span class="ribbon-text">Rotate</span>
            </button>
            <button class="ribbon-btn danger" @click="deleteSelected" :disabled="selectedElements.length === 0" title="Delete (Del)">
              <span class="ribbon-icon">🗑️</span>
              <span class="ribbon-text">Delete</span>
            </button>
            <button class="ribbon-btn" @click="clearAll" title="Clear All">
              <span class="ribbon-icon">🧹</span>
              <span class="ribbon-text">Clear</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">History</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="undo" :disabled="!canUndo" title="Undo (Ctrl+Z)">
              <span class="ribbon-icon">↶</span>
              <span class="ribbon-text">Undo</span>
            </button>
            <button class="ribbon-btn" @click="redo" :disabled="!canRedo" title="Redo (Ctrl+Y)">
              <span class="ribbon-icon">↷</span>
              <span class="ribbon-text">Redo</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">Alignment</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="alignLeft" :disabled="selectedElements.length < 2" title="Align Left">
              <span class="ribbon-icon">⬅️</span>
              <span class="ribbon-text">Left</span>
            </button>
            <button class="ribbon-btn" @click="alignCenter" :disabled="selectedElements.length < 2" title="Align Center">
              <span class="ribbon-icon">↔️</span>
              <span class="ribbon-text">Center</span>
            </button>
            <button class="ribbon-btn" @click="alignRight" :disabled="selectedElements.length < 2" title="Align Right">
              <span class="ribbon-icon">➡️</span>
              <span class="ribbon-text">Right</span>
            </button>
            <button class="ribbon-btn" @click="alignTop" :disabled="selectedElements.length < 2" title="Align Top">
              <span class="ribbon-icon">⬆️</span>
              <span class="ribbon-text">Top</span>
            </button>
            <button class="ribbon-btn" @click="alignMiddle" :disabled="selectedElements.length < 2" title="Align Middle">
              <span class="ribbon-icon">↕️</span>
              <span class="ribbon-text">Middle</span>
            </button>
            <button class="ribbon-btn" @click="alignBottom" :disabled="selectedElements.length < 2" title="Align Bottom">
              <span class="ribbon-icon">⬇️</span>
              <span class="ribbon-text">Bottom</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">Arrange</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="bringToFront" :disabled="selectedElements.length === 0" title="Bring to Front">
              <span class="ribbon-icon">⬆️</span>
              <span class="ribbon-text">Front</span>
            </button>
            <button class="ribbon-btn" @click="sendToBack" :disabled="selectedElements.length === 0" title="Send to Back">
              <span class="ribbon-icon">⬇️</span>
              <span class="ribbon-text">Back</span>
            </button>
            <button class="ribbon-btn" @click="groupElements" :disabled="selectedElements.length < 2" title="Group (Ctrl+G)">
              <span class="ribbon-icon">📦</span>
              <span class="ribbon-text">Group</span>
            </button>
            <button class="ribbon-btn" @click="ungroupElements" :disabled="!hasGroupSelected" title="Ungroup (Ctrl+Shift+G)">
              <span class="ribbon-icon">📤</span>
              <span class="ribbon-text">Ungroup</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">View</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="zoomIn" title="Zoom In (+)">
              <span class="ribbon-icon">🔍</span>
              <span class="ribbon-text">Zoom In</span>
            </button>
            <button class="ribbon-btn" @click="zoomOut" title="Zoom Out (-)">
              <span class="ribbon-icon">🔍</span>
              <span class="ribbon-text">Zoom Out</span>
            </button>
            <button class="ribbon-btn" @click="resetZoom" title="Reset Zoom (0)">
              <span class="ribbon-icon">📐</span>
              <span class="ribbon-text">100%</span>
            </button>
            <button class="ribbon-btn" @click="toggleGrid" :class="{ active: showGrid }" title="Toggle Grid (G)">
              <span class="ribbon-icon">⚏</span>
              <span class="ribbon-text">Grid</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">Export</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="exportAsPNG" title="Export as PNG">
              <span class="ribbon-icon">🖼️</span>
              <span class="ribbon-text">PNG</span>
            </button>
            <button class="ribbon-btn" @click="exportAsSVG" title="Export as SVG">
              <span class="ribbon-icon">📐</span>
              <span class="ribbon-text">SVG</span>
            </button>
          </div>
        </div>

        <div class="ribbon-separator"></div>

        <div class="ribbon-group">
          <label class="ribbon-label">Project</label>
          <div class="ribbon-buttons">
            <button class="ribbon-btn" @click="saveProject" title="Save (Ctrl+S)">
              <span class="ribbon-icon">💾</span>
              <span class="ribbon-text">Save</span>
            </button>
            <button class="ribbon-btn" @click="triggerLoadProject" title="Load Project">
              <span class="ribbon-icon">📁</span>
              <span class="ribbon-text">Load</span>
            </button>
            <input ref="fileInput" type="file" @change="loadProject" accept=".json" style="display: none;">
          </div>
        </div>
      </div>
    </div>

    <div class="editor-container">
      <!-- Toolbar -->
      <div class="toolbar">
        <h3>Visual Editor</h3>


          <div class="tool-group">
            <label>Add Elements</label>
            <button class="btn tooltip" @click="addTextElement('heading')" @dragstart="startDragFromSidebar('text-heading', $event)" draggable="true" data-tooltip="Add Heading">
              <span class="btn-icon">📝</span> H1
            </button>
            <button class="btn tooltip" @click="addTextElement('paragraph')" @dragstart="startDragFromSidebar('text-paragraph', $event)" draggable="true" data-tooltip="Add Paragraph">
              <span class="btn-icon">📄</span> P
            </button>
            <button class="btn tooltip" @click="addTextElement('label')" @dragstart="startDragFromSidebar('text-label', $event)" draggable="true" data-tooltip="Add Label">
              <span class="btn-icon">🏷️</span> Label
            </button>
          </div>

        <div class="tool-group">
          <label>Basic Shapes</label>
          <div class="library-grid">
            <button class="lib-btn" @click="addShape('rectangle')" @dragstart="startDragFromSidebar('rectangle', $event)" draggable="true" title="Rectangle">▭</button>
            <button class="lib-btn" @click="addShape('square')" @dragstart="startDragFromSidebar('square', $event)" draggable="true" title="Square">⬜</button>
            <button class="lib-btn" @click="addShape('circle')" @dragstart="startDragFromSidebar('circle', $event)" draggable="true" title="Circle">○</button>
            <button class="lib-btn" @click="addShape('oval')" @dragstart="startDragFromSidebar('oval', $event)" draggable="true" title="Oval">⬭</button>
            <button class="lib-btn" @click="addShape('triangle')" @dragstart="startDragFromSidebar('triangle', $event)" draggable="true" title="Triangle">△</button>
            <button class="lib-btn" @click="addShape('diamond')" @dragstart="startDragFromSidebar('diamond', $event)" draggable="true" title="Diamond">◇</button>
          </div>
        </div>

        <div class="tool-group">
          <label>Drawing Tools</label>
          <div class="library-grid">
            <button class="lib-btn" @click="addShape('line')" @dragstart="startDragFromSidebar('line', $event)" draggable="true" title="Straight Line">─</button>
            <button class="lib-btn" @click="addShape('arrow')" @dragstart="startDragFromSidebar('arrow', $event)" draggable="true" title="Arrow">→</button>
            <button class="lib-btn" @click="addShape('curve')" @dragstart="startDragFromSidebar('curve', $event)" draggable="true" title="Curve">⌒</button>
            <button class="lib-btn" @click="addShape('polygon')" @dragstart="startDragFromSidebar('polygon', $event)" draggable="true" title="Polygon">⬟</button>
            <button class="lib-btn" @click="addShape('dotted-line')" @dragstart="startDragFromSidebar('dotted-line', $event)" draggable="true" title="Dotted Line">┅</button>
            <button class="lib-btn" @click="addShape('dotted-curve')" @dragstart="startDragFromSidebar('dotted-curve', $event)" draggable="true" title="Dotted Curve">⋯</button>
          </div>
        </div>

        <div class="tool-group">
          <label>Advanced Shapes</label>
          <div class="library-grid">
            <button class="lib-btn" @click="addShape('hexagon')" @dragstart="startDragFromSidebar('hexagon', $event)" draggable="true" title="Hexagon">⬡</button>
            <button class="lib-btn" @click="addShape('octagon')" @dragstart="startDragFromSidebar('octagon', $event)" draggable="true" title="Octagon">⯃</button>
            <button class="lib-btn" @click="addShape('parallelogram')" @dragstart="startDragFromSidebar('parallelogram', $event)" draggable="true" title="Parallelogram">▱</button>
            <button class="lib-btn" @click="addShape('trapezoid')" @dragstart="startDragFromSidebar('trapezoid', $event)" draggable="true" title="Trapezoid">⏢</button>
            <button class="lib-btn" @click="addShape('cross')" @dragstart="startDragFromSidebar('cross', $event)" draggable="true" title="Cross">✚</button>
            <button class="lib-btn" @click="addShape('rounded-rect')" @dragstart="startDragFromSidebar('rounded-rect', $event)" draggable="true" title="Rounded Rectangle">▢</button>
          </div>
        </div>

        <div class="tool-group">
          <label>Media Upload</label>
          <div class="media-controls">
            <button class="btn tooltip" @click="triggerImageUpload" data-tooltip="Upload Image File">
              <span class="btn-icon">🖼️</span> Upload Image
            </button>
            <input ref="imageInput" type="file" @change="handleImageUpload" accept="image/*" style="display: none;">

            <div class="url-input-group">
              <input
                v-model="imageUrl"
                type="url"
                placeholder="Enter image URL..."
                class="url-input"
                @keyup.enter="addImageFromUrl"
              >
              <button class="btn tooltip" @click="addImageFromUrl" :disabled="!imageUrl" data-tooltip="Add Image from URL">
                <span class="btn-icon">🔗</span> Add URL
              </button>
            </div>
          </div>
        </div>

        <div class="tool-group">
          <label>Icons</label>
          <div class="library-grid">
            <!-- Common Icons Row 1 -->
            <button class="lib-btn" @click="addIcon('star')" title="Star">⭐</button>
            <button class="lib-btn" @click="addIcon('heart')" title="Heart">❤️</button>
            <button class="lib-btn" @click="addIcon('check')" title="Check">✅</button>
            <button class="lib-btn" @click="addIcon('cross')" title="Cross">❌</button>
            <button class="lib-btn" @click="addIcon('warning')" title="Warning">⚠️</button>
            <button class="lib-btn" @click="addIcon('info')" title="Info">ℹ️</button>

            <!-- Common Icons Row 2 -->
            <button class="lib-btn" @click="addIcon('home')" title="Home">🏠</button>
            <button class="lib-btn" @click="addIcon('user')" title="User">👤</button>
            <button class="lib-btn" @click="addIcon('mail')" title="Mail">📧</button>
            <button class="lib-btn" @click="addIcon('phone')" title="Phone">📞</button>
            <button class="lib-btn" @click="addIcon('location')" title="Location">📍</button>
            <button class="lib-btn" @click="addIcon('calendar')" title="Calendar">📅</button>

            <!-- Common Icons Row 3 -->
            <button class="lib-btn" @click="addIcon('clock')" title="Clock">🕐</button>
            <button class="lib-btn" @click="addIcon('settings')" title="Settings">⚙️</button>
            <button class="lib-btn" @click="addIcon('search')" title="Search">🔍</button>
            <button class="lib-btn" @click="addIcon('download')" title="Download">⬇️</button>
            <button class="lib-btn" @click="addIcon('upload')" title="Upload">⬆️</button>
            <button class="lib-btn" @click="showIconLibrary" title="More Icons...">➕</button>
          </div>
        </div>






      </div>

      <!-- Canvas -->
      <div class="canvas-container">
        <div
          class="canvas"
          ref="canvas"
          @click="handleCanvasClick"
          @dblclick="handleCanvasDoubleClick"
          @mousedown="handleCanvasMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @keydown="handleKeyDown"
          @contextmenu="handleCanvasContextMenu"
          @dragover.prevent
          @drop="handleCanvasDrop"

          tabindex="0"
          :style="getCanvasStyle()"
        >
          <!-- Elements -->
          <div
            v-for="element in elements"
            :key="element.id"
            :class="['element', element.type, { selected: selectedElements.includes(element.id) }]"
            :style="getElementStyle(element)"
            @click.stop="selectElement(element.id, $event)"
            @mousedown.stop="startDragging(element.id, $event)"
            @contextmenu.prevent="handleElementContextMenu(element.id, $event)"
            :title="`Element: ${element.type} (${element.id})`"
          >
            <!-- Text Elements -->
            <div v-if="element.type === 'text'"
                 :data-element-id="element.id"
                 :contenteditable="element.isEditing || selectedElements.includes(element.id)"
                 @blur="stopEditing(element.id, $event)"
                 @dblclick.stop="startEditing(element.id)"
                 @keydown.enter.prevent="stopEditing(element.id, $event)"
                 v-html="element.content">
            </div>
            
            <!-- Shape Elements -->
            <div v-else-if="element.type === 'shape'"
                 :style="getShapeStyle(element)">
              <span v-if="element.subtype === 'star'">⭐</span>
              <span v-else-if="element.subtype === 'heart'">♥</span>

              <!-- SVG for arrow -->
              <svg v-else-if="element.subtype === 'arrow'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <!-- Arrow line -->
                <line x1="10" y1="50" x2="75" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
                <!-- Arrow head -->
                <polygon points="75,40 90,50 75,60"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>
              <!-- SVG for triangle -->
              <svg v-else-if="element.subtype === 'triangle'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="50,5 5,95 95,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>
              <!-- SVG for trapezoid -->
              <svg v-else-if="element.subtype === 'trapezoid'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="20,5 80,5 95,95 5,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for hexagon -->
              <svg v-else-if="element.subtype === 'hexagon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="25,5 75,5 95,35 75,95 25,95 5,35"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for octagon -->
              <svg v-else-if="element.subtype === 'octagon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="30,10 70,10 90,30 90,70 70,90 30,90 10,70 10,30"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for parallelogram -->
              <svg v-else-if="element.subtype === 'parallelogram'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="20,5 95,5 80,95 5,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for cross -->
              <svg v-else-if="element.subtype === 'cross'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="35,5 65,5 65,35 95,35 95,65 65,65 65,95 35,95 35,65 5,65 5,35 35,35"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for polygon -->
              <svg v-else-if="element.subtype === 'polygon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="50,5 85,25 85,75 50,95 15,75 15,25"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for curve -->
              <svg v-else-if="element.subtype === 'curve'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <path d="M 10,80 Q 50,20 90,80"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      fill="none"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for dotted line -->
              <svg v-else-if="element.subtype === 'dotted-line'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <line x1="10" y1="50" x2="90" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-dasharray="5,5"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for dotted curve -->
              <svg v-else-if="element.subtype === 'dotted-curve'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <path d="M 10,80 Q 50,20 90,80"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      fill="none"
                      stroke-dasharray="5,5"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>
            </div>
            
            <!-- Icon Elements -->
            <div v-else-if="element.type === 'icon'"
                 :style="getIconStyle(element)">
              {{ element.content }}
            </div>

            <!-- Image Elements -->
            <img v-else-if="element.type === 'image'"
                 :src="element.src"
                 :alt="element.name"
                 :style="getImageStyle(element)"
                 draggable="false">
            </img>

            <!-- Custom SVG Elements -->
            <div v-else-if="element.type === 'custom-svg'"
                 :style="getCustomSvgStyle(element)"
                 v-html="element.svgCode">
            </div>

            <!-- Resize Handles -->
            <div v-if="selectedElements.includes(element.id)" class="resize-handles">
              <div class="resize-handle nw" @mousedown.stop="startResizing(element.id, 'nw', $event)"></div>
              <div class="resize-handle ne" @mousedown.stop="startResizing(element.id, 'ne', $event)"></div>
              <div class="resize-handle sw" @mousedown.stop="startResizing(element.id, 'sw', $event)"></div>
              <div class="resize-handle se" @mousedown.stop="startResizing(element.id, 'se', $event)"></div>
              <!-- Rotation Handles -->
              <div class="rotate-handle rotate-top" @mousedown.stop="startRotating(element.id, $event)" title="Drag to rotate">🔄</div>
              <div class="rotate-handle rotate-bottom" @mousedown.stop="startRotating(element.id, $event)" title="Drag to rotate">🔄</div>
            </div>
          </div>



          <!-- Selection Area -->
          <div v-if="selectionArea.active"
               class="selection-area"
               :style="{
                 left: Math.min(selectionArea.startX, selectionArea.endX) + 'px',
                 top: Math.min(selectionArea.startY, selectionArea.endY) + 'px',
                 width: Math.abs(selectionArea.endX - selectionArea.startX) + 'px',
                 height: Math.abs(selectionArea.endY - selectionArea.startY) + 'px'
               }">
          </div>

          <!-- Context Menu -->
          <div
            v-if="contextMenu.visible"
            class="context-menu"
            :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
            @click.stop
          >
            <div v-if="isTextElement(contextMenu.elementId)" class="context-menu-item" @click="editFromContext">
              ✏️ Edit
            </div>
            <div class="context-menu-item" @click="duplicateFromContext">
              📋 Duplicate
            </div>
            <div class="context-menu-item" @click="copyFromContext">
              📄 Copy
            </div>
            <div class="context-menu-item" @click="pasteFromContext">
              📋 Paste
            </div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item danger" @click="deleteFromContext">
              🗑️ Delete
            </div>
          </div>
        </div>
      </div>

      <!-- Layers Panel -->
      <div class="layers-panel">
        <h3>Layers ({{ elements.length }})</h3>
        
        <div class="layers-controls">
          <button class="btn-small" @click="lockSelected" :disabled="selectedElements.length === 0" title="Lock Selected">
            🔒 Lock
          </button>
          <button class="btn-small" @click="unlockSelected" :disabled="selectedElements.length === 0" title="Unlock Selected">
            🔓 Unlock
          </button>
          <button class="btn-small" @click="hideSelected" :disabled="selectedElements.length === 0" title="Hide Selected">
            👁️ Hide
          </button>
        </div>

        <div class="layers-list">
          <div
            v-for="(element, index) in layeredElements"
            :key="element.id"
            :class="['layer-item', { 
              selected: selectedElements.includes(element.id),
              locked: element.locked,
              hidden: element.hidden
            }]"
            @click="selectLayer(element.id, $event)"
            @dblclick="renameLayer(element.id)"
          >
            <div class="layer-info">
              <span class="layer-icon">{{ getLayerIcon(element) }}</span>
              <span class="layer-name" :contenteditable="element.isRenaming" @blur="finishRename(element.id, $event)">
                {{ element.name || `${element.type} ${index + 1}` }}
              </span>
            </div>
            <div class="layer-controls">
              <button
                class="layer-btn"
                @click.stop="toggleVisibility(element.id)"
                :title="element.hidden ? 'Show' : 'Hide'"
              >
                {{ element.hidden ? '👁️‍🗨️' : '👁️' }}
              </button>
              <button
                class="layer-btn"
                @click.stop="toggleLock(element.id)"
                :title="element.locked ? 'Unlock' : 'Lock'"
              >
                {{ element.locked ? '🔒' : '🔓' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Properties Panel -->
      <div class="properties-panel" v-if="selectedElements.length > 0">
        <h3>Properties ({{ selectedElements.length }} selected)</h3>

        <!-- Position & Size -->
        <div class="property-section">
          <div class="property-item">
            <label>Position</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.x" @input="updateAllSelectedElements('x', firstSelectedElement.x)" placeholder="X">
              <input type="number" v-model.number="firstSelectedElement.y" @input="updateAllSelectedElements('y', firstSelectedElement.y)" placeholder="Y">
            </div>
          </div>

          <div class="property-item">
            <label>Size</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.width" @input="updateAllSelectedElements('width', firstSelectedElement.width)" placeholder="Width">
              <input type="number" v-model.number="firstSelectedElement.height" @input="updateAllSelectedElements('height', firstSelectedElement.height)" placeholder="Height">
            </div>
          </div>

          <div class="property-item">
            <label>Rotation</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.rotation" @input="updateAllSelectedElements('rotation', firstSelectedElement.rotation)" placeholder="Degrees" min="0" max="359">
              <button class="btn-small" @click="rotateSelected">🔄 +90°</button>
            </div>
          </div>

          <div class="property-item">
            <label>Opacity</label>
            <div class="input-group">
              <input type="range" v-model.number="firstSelectedElement.opacity" @input="updateAllSelectedElements('opacity', firstSelectedElement.opacity)" min="0" max="1" step="0.1" class="opacity-slider">
              <span class="unit-label">{{ Math.round((firstSelectedElement.opacity || 1) * 100) }}%</span>
            </div>
          </div>

          <!-- Border/Thickness (for shapes and lines) -->
          <div class="property-item" v-if="hasShapesOrLines">
            <label>Border Thickness</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.borderWidth" @input="updateAllSelectedElements('borderWidth', firstSelectedElement.borderWidth)" placeholder="Width" min="1" max="20">
              <span class="unit-label">px</span>
            </div>
          </div>
        </div>

        <!-- Color Controls -->
        <div class="property-section">
          <h4>Colors</h4>

          <!-- Fill Color (for shapes and icons) -->
          <div class="property-item" v-if="hasShapesOrIcons">
            <label>Fill Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedFillColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Background/Fill</span>
            </div>
          </div>

          <!-- Border Color (for shapes and images) -->
          <div class="property-item" v-if="hasShapesOrImages">
            <label>Border Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedBorderColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Edge/Border</span>
            </div>
          </div>

          <!-- Text Color (for text elements) -->
          <div class="property-item" v-if="hasTextElements">
            <label>Text Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedTextColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Text/Font</span>
            </div>
          </div>

          <!-- Font Properties (for text elements) -->
          <div v-if="hasTextElements" class="property-section">
            <h4>Font</h4>

            <div class="property-item">
              <label>Font Family</label>
              <select v-model="firstSelectedElement.fontFamily" @change="updateAllSelectedElements('fontFamily', firstSelectedElement.fontFamily)" class="font-select">
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Times New Roman, serif">Times New Roman</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="Verdana, sans-serif">Verdana</option>
                <option value="Courier New, monospace">Courier New</option>
                <option value="Impact, sans-serif">Impact</option>
                <option value="Comic Sans MS, cursive">Comic Sans MS</option>
              </select>
            </div>

            <div class="property-item">
              <label>Font Size</label>
              <div class="input-group">
                <input type="number" v-model.number="firstSelectedElement.fontSize" @input="updateAllSelectedElements('fontSize', firstSelectedElement.fontSize)" placeholder="Size" min="8" max="72">
                <span class="unit-label">px</span>
              </div>
            </div>

            <div class="property-item">
              <label>Font Weight</label>
              <select v-model="firstSelectedElement.fontWeight" @change="updateAllSelectedElements('fontWeight', firstSelectedElement.fontWeight)" class="font-select">
                <option value="normal">Normal</option>
                <option value="bold">Bold</option>
                <option value="lighter">Lighter</option>
                <option value="bolder">Bolder</option>
              </select>
            </div>

            <div class="property-item">
              <label>Text Align</label>
              <select v-model="firstSelectedElement.textAlign" @change="updateAllSelectedElements('textAlign', firstSelectedElement.textAlign)" class="font-select">
                <option value="left">Left</option>
                <option value="center">Center</option>
                <option value="right">Right</option>
                <option value="justify">Justify</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Icon Library Modal -->
    <div v-if="iconLibraryVisible" class="modal-overlay" @click="hideIconLibrary">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Icon Library</h3>
          <button class="modal-close" @click="hideIconLibrary">✕</button>
        </div>

        <div class="modal-body">
          <!-- Icon Categories -->
          <div class="icon-categories">
            <button
              v-for="category in iconCategories"
              :key="category.name"
              :class="['category-btn', { active: selectedCategory === category.name }]"
              @click="selectedCategory = category.name"
            >
              {{ category.label }}
            </button>
          </div>

          <!-- Icon Grid -->
          <div class="icon-grid">
            <button
              v-for="icon in currentCategoryIcons"
              :key="icon.name"
              class="icon-item"
              @click="addIconFromLibrary(icon)"
              :title="icon.name"
            >
              {{ icon.symbol }}
            </button>
          </div>

          <!-- Custom SVG Section -->
          <div class="custom-svg-section">
            <h4>Custom SVG</h4>
            <textarea
              v-model="customSvgCode"
              placeholder="Paste your SVG code here..."
              class="svg-textarea"
            ></textarea>
            <button class="btn primary" @click="addCustomSvg" :disabled="!customSvgCode.trim()">
              Add Custom SVG
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'

// State
const elements = ref([])
const selectedElements = ref([])
const history = ref([])
const historyIndex = ref(-1)
const isDragging = ref(false)
const isResizing = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const mousePosition = ref({ x: 0, y: 0 })
const canvasBackground = ref('#ffffff')
const canvas = ref(null)
const fileInput = ref(null)
const imageInput = ref(null)
const imageUrl = ref('')
const draggedElement = ref(null)
const resizeData = ref({ elementId: null, handle: null, startX: 0, startY: 0, startWidth: 0, startHeight: 0 })
const contextMenu = ref({ visible: false, x: 0, y: 0, elementId: null })
const clipboard = ref(null)

// Rotation state
const isRotating = ref(false)
const rotateData = ref({ elementId: null, startAngle: 0, centerX: 0, centerY: 0 })

// Selection area for drag selection
const selectionArea = ref({ active: false, startX: 0, startY: 0, endX: 0, endY: 0 })
const isSelecting = ref(false)

// Color controls
const selectedFillColor = ref('#ffffff')
const selectedBorderColor = ref('#000000')
const selectedTextColor = ref('#000000')
const selectedShadowColor = ref('#000000')

// View controls
const zoomLevel = ref(1)
const showGrid = ref(false)
const canvasOffset = ref({ x: 0, y: 0 })

// Icon library
const iconLibraryVisible = ref(false)
const selectedCategory = ref('common')
const customSvgCode = ref('')

// Computed
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// Color controls computed
const hasTextElements = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'text'
  })
})

const hasShapesOrIcons = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'icon')
  })
})

const hasShapesOrImages = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'image')
  })
})

const hasShapesOrLines = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'shape'
  })
})

const firstSelectedElement = computed(() => {
  if (selectedElements.value.length > 0) {
    return getElementById(selectedElements.value[0]) || {}
  }
  return {}
})

const hasShapesOrImagesOrText = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'image' || element.type === 'text')
  })
})

const hasGroupSelected = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'group'
  })
})

const layeredElements = computed(() => {
  return [...elements.value].reverse() // Show top elements first in layers panel
})

// Icon library data
const iconCategories = ref([
  { name: 'common', label: 'Common' },
  { name: 'arrows', label: 'Arrows' },
  { name: 'symbols', label: 'Symbols' },
  { name: 'objects', label: 'Objects' },
  { name: 'nature', label: 'Nature' },
  { name: 'people', label: 'People' }
])

const iconLibrary = ref({
  common: [
    { name: 'star', symbol: '⭐' },
    { name: 'heart', symbol: '❤️' },
    { name: 'check', symbol: '✅' },
    { name: 'cross', symbol: '❌' },
    { name: 'warning', symbol: '⚠️' },
    { name: 'info', symbol: 'ℹ️' },
    { name: 'home', symbol: '🏠' },
    { name: 'user', symbol: '👤' },
    { name: 'mail', symbol: '📧' },
    { name: 'phone', symbol: '📞' },
    { name: 'location', symbol: '📍' },
    { name: 'calendar', symbol: '📅' },
    { name: 'clock', symbol: '🕐' },
    { name: 'settings', symbol: '⚙️' },
    { name: 'search', symbol: '🔍' },
    { name: 'download', symbol: '⬇️' },
    { name: 'upload', symbol: '⬆️' },
    { name: 'save', symbol: '💾' },
    { name: 'edit', symbol: '✏️' },
    { name: 'delete', symbol: '🗑️' }
  ],
  arrows: [
    { name: 'arrow-up', symbol: '⬆️' },
    { name: 'arrow-down', symbol: '⬇️' },
    { name: 'arrow-left', symbol: '⬅️' },
    { name: 'arrow-right', symbol: '➡️' },
    { name: 'arrow-up-right', symbol: '↗️' },
    { name: 'arrow-down-right', symbol: '↘️' },
    { name: 'arrow-down-left', symbol: '↙️' },
    { name: 'arrow-up-left', symbol: '↖️' },
    { name: 'refresh', symbol: '🔄' },
    { name: 'undo', symbol: '↩️' },
    { name: 'redo', symbol: '↪️' },
    { name: 'forward', symbol: '⏩' },
    { name: 'backward', symbol: '⏪' },
    { name: 'play', symbol: '▶️' },
    { name: 'pause', symbol: '⏸️' },
    { name: 'stop', symbol: '⏹️' }
  ],
  symbols: [
    { name: 'plus', symbol: '➕' },
    { name: 'minus', symbol: '➖' },
    { name: 'multiply', symbol: '✖️' },
    { name: 'divide', symbol: '➗' },
    { name: 'equals', symbol: '🟰' },
    { name: 'question', symbol: '❓' },
    { name: 'exclamation', symbol: '❗' },
    { name: 'copyright', symbol: '©️' },
    { name: 'trademark', symbol: '™️' },
    { name: 'registered', symbol: '®️' },
    { name: 'dollar', symbol: '💲' },
    { name: 'euro', symbol: '💶' },
    { name: 'pound', symbol: '💷' },
    { name: 'yen', symbol: '💴' },
    { name: 'percent', symbol: '💯' },
    { name: 'hash', symbol: '#️⃣' }
  ],
  objects: [
    { name: 'computer', symbol: '💻' },
    { name: 'mobile', symbol: '📱' },
    { name: 'tablet', symbol: '📱' },
    { name: 'camera', symbol: '📷' },
    { name: 'printer', symbol: '🖨️' },
    { name: 'keyboard', symbol: '⌨️' },
    { name: 'mouse', symbol: '🖱️' },
    { name: 'monitor', symbol: '🖥️' },
    { name: 'speaker', symbol: '🔊' },
    { name: 'microphone', symbol: '🎤' },
    { name: 'headphones', symbol: '🎧' },
    { name: 'book', symbol: '📚' },
    { name: 'folder', symbol: '📁' },
    { name: 'file', symbol: '📄' },
    { name: 'image', symbol: '🖼️' },
    { name: 'video', symbol: '🎥' }
  ],
  nature: [
    { name: 'sun', symbol: '☀️' },
    { name: 'moon', symbol: '🌙' },
    { name: 'star-nature', symbol: '⭐' },
    { name: 'cloud', symbol: '☁️' },
    { name: 'rain', symbol: '🌧️' },
    { name: 'snow', symbol: '❄️' },
    { name: 'lightning', symbol: '⚡' },
    { name: 'fire', symbol: '🔥' },
    { name: 'water', symbol: '💧' },
    { name: 'earth', symbol: '🌍' },
    { name: 'tree', symbol: '🌳' },
    { name: 'flower', symbol: '🌸' },
    { name: 'leaf', symbol: '🍃' },
    { name: 'mountain', symbol: '⛰️' },
    { name: 'ocean', symbol: '🌊' },
    { name: 'rainbow', symbol: '🌈' }
  ],
  people: [
    { name: 'person', symbol: '👤' },
    { name: 'people', symbol: '👥' },
    { name: 'family', symbol: '👨‍👩‍👧‍👦' },
    { name: 'man', symbol: '👨' },
    { name: 'woman', symbol: '👩' },
    { name: 'child', symbol: '🧒' },
    { name: 'baby', symbol: '👶' },
    { name: 'elder', symbol: '👴' },
    { name: 'thumbs-up', symbol: '👍' },
    { name: 'thumbs-down', symbol: '👎' },
    { name: 'clap', symbol: '👏' },
    { name: 'wave', symbol: '👋' },
    { name: 'peace', symbol: '✌️' },
    { name: 'ok-hand', symbol: '👌' },
    { name: 'pointing', symbol: '👉' },
    { name: 'muscle', symbol: '💪' }
  ]
})

const currentCategoryIcons = computed(() => {
  return iconLibrary.value[selectedCategory.value] || []
})

// Helper functions
const generateId = () => Math.random().toString(36).substr(2, 9)

const getElementById = (id) => elements.value.find(el => el.id === id)

// Alignment functions
const alignLeft = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const leftmost = Math.min(...selectedElements.value.map(id => getElementById(id).x))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = leftmost
  })
}

const alignRight = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const rightmost = Math.max(...selectedElements.value.map(id => {
    const el = getElementById(id)
    return el.x + el.width
  }))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = rightmost - element.width
  })
}

const alignCenter = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const centerX = selectedElements.value.reduce((sum, id) => {
    const el = getElementById(id)
    return sum + el.x + el.width / 2
  }, 0) / selectedElements.value.length
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = centerX - element.width / 2
  })
}

const alignTop = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const topmost = Math.min(...selectedElements.value.map(id => getElementById(id).y))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = topmost
  })
}

const alignBottom = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const bottommost = Math.max(...selectedElements.value.map(id => {
    const el = getElementById(id)
    return el.y + el.height
  }))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = bottommost - element.height
  })
}

const alignMiddle = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const centerY = selectedElements.value.reduce((sum, id) => {
    const el = getElementById(id)
    return sum + el.y + el.height / 2
  }, 0) / selectedElements.value.length
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = centerY - element.height / 2
  })
}

// Layer management functions
const bringToFront = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const index = elements.value.findIndex(el => el.id === id)
      elements.value.splice(index, 1)
      elements.value.push(element)
    }
  })
}

const sendToBack = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const index = elements.value.findIndex(el => el.id === id)
      elements.value.splice(index, 1)
      elements.value.unshift(element)
    }
  })
}

// Zoom functions
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.25, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.25, 0.25)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const toggleGrid = () => {
  showGrid.value = !showGrid.value
}

// Layer panel functions
const selectLayer = (id, event) => {
  selectElement(id, event)
}

const getLayerIcon = (element) => {
  switch (element.type) {
    case 'text': return '📝'
    case 'shape': return '🔷'
    case 'icon': return '🎭'
    case 'image': return '🖼️'
    case 'group': return '📦'
    default: return '📄'
  }
}

const toggleVisibility = (id) => {
  const element = getElementById(id)
  if (element) {
    element.hidden = !element.hidden
  }
}

const toggleLock = (id) => {
  const element = getElementById(id)
  if (element) {
    element.locked = !element.locked
  }
}

const lockSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.locked = true
  })
}

const unlockSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.locked = false
  })
}

const hideSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.hidden = true
  })
}

const renameLayer = (id) => {
  const element = getElementById(id)
  if (element) {
    element.isRenaming = true
  }
}

const finishRename = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.name = event.target.textContent
    element.isRenaming = false
  }
}

// Group functions
const groupElements = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  
  const groupedElements = selectedElements.value.map(id => getElementById(id))
  const bounds = getBounds(groupedElements)
  
  const group = {
    id: generateId(),
    type: 'group',
    x: bounds.left,
    y: bounds.top,
    width: bounds.width,
    height: bounds.height,
    children: [...selectedElements.value],
    rotation: 0,
    opacity: 1
  }
  
  // Remove individual elements from main array
  elements.value = elements.value.filter(el => !selectedElements.value.includes(el.id))
  
  // Add group
  elements.value.push(group)
  selectedElements.value = [group.id]
}

const ungroupElements = () => {
  const groupIds = selectedElements.value.filter(id => {
    const element = getElementById(id)
    return element && element.type === 'group'
  })
  
  if (groupIds.length === 0) return
  saveState()
  
  groupIds.forEach(groupId => {
    const group = getElementById(groupId)
    if (group && group.children) {
      // Add children back to main array
      group.children.forEach(childId => {
        const child = getElementById(childId)
        if (child) {
          elements.value.push(child)
        }
      })
      
      // Remove group
      elements.value = elements.value.filter(el => el.id !== groupId)
    }
  })
  
  selectedElements.value = []
}

const getBounds = (elements) => {
  const left = Math.min(...elements.map(el => el.x))
  const top = Math.min(...elements.map(el => el.y))
  const right = Math.max(...elements.map(el => el.x + el.width))
  const bottom = Math.max(...elements.map(el => el.y + el.height))
  
  return {
    left,
    top,
    right,
    bottom,
    width: right - left,
    height: bottom - top
  }
}

// Export functions
const exportAsPNG = () => {
  const canvasEl = canvas.value
  const rect = canvasEl.getBoundingClientRect()
  
  // Create a temporary canvas
  const tempCanvas = document.createElement('canvas')
  const ctx = tempCanvas.getContext('2d')
  
  // Set canvas size
  tempCanvas.width = rect.width
  tempCanvas.height = rect.height
  
  // Fill background
  ctx.fillStyle = canvasBackground.value
  ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height)
  
  // This is a simplified export - in a real app, you'd need to render each element
  alert('PNG export functionality requires additional canvas rendering implementation')
}

const exportAsSVG = () => {
  let svg = `<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">`
  svg += `<rect width="100%" height="100%" fill="${canvasBackground.value}"/>`
  
  elements.value.forEach(element => {
    if (element.type === 'shape' && element.subtype === 'rectangle') {
      svg += `<rect x="${element.x}" y="${element.y}" width="${element.width}" height="${element.height}" fill="${element.color}" stroke="${element.borderColor}" stroke-width="${element.borderWidth}"/>`
    } else if (element.type === 'text') {
      svg += `<text x="${element.x}" y="${element.y + element.fontSize}" font-family="${element.fontFamily}" font-size="${element.fontSize}" fill="${element.color}">${element.content}</text>`
    }
  })
  
  svg += '</svg>'
  
  const blob = new Blob([svg], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `visual-editor-export-${Date.now()}.svg`
  a.click()
  URL.revokeObjectURL(url)
}

// Shadow effect functions
const updateSelectedShadow = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element.shadowColor = selectedShadowColor.value
    }
  })
}

const saveState = () => {
  const newState = JSON.parse(JSON.stringify(elements.value))
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(newState)
  historyIndex.value++
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

// Drag and Drop from Sidebar
const startDragFromSidebar = (elementType, event) => {
  event.dataTransfer.setData('text/plain', elementType)
  event.dataTransfer.effectAllowed = 'copy'
}

const handleCanvasDrop = (event) => {
  event.preventDefault()
  const elementType = event.dataTransfer.getData('text/plain')

  if (!elementType) return

  const rect = canvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // Handle text elements
  if (elementType.startsWith('text-')) {
    const textType = elementType.replace('text-', '')
    addTextElementAtPosition(textType, x, y)
  } else {
    // Handle shape elements
    addShapeAtPosition(elementType, x, y)
  }
}

const addTextElementAtPosition = (type, x, y) => {
  saveState()
  const element = {
    id: generateId(),
    type: 'text',
    subtype: type,
    content: type === 'heading' ? 'Heading text' : type === 'paragraph' ? 'Paragraph text' : 'Label text',
    x: x - 50, // Center the element
    y: y - 15,
    width: 100,
    height: 30,
    color: '#000000',
    fontSize: type === 'heading' ? 24 : type === 'paragraph' ? 16 : 14,
    fontFamily: 'Arial, sans-serif',
    fontWeight: type === 'heading' ? 'bold' : 'normal',
    textAlign: 'left',
    rotation: 0
  }
  elements.value.push(element)
  selectedElements.value = [element.id]
}

const addShapeAtPosition = (shapeType, x, y) => {
  saveState()

  // Shape-specific dimensions and properties
  const shapeDefaults = {
    rectangle: { width: 120, height: 80 },
    square: { width: 80, height: 80 },
    circle: { width: 80, height: 80 },
    oval: { width: 120, height: 80 },
    triangle: { width: 80, height: 80 },
    diamond: { width: 80, height: 80 },
    line: { width: 100, height: 2 },
    arrow: { width: 100, height: 20 },
    curve: { width: 100, height: 50 },
    polygon: { width: 80, height: 80 },
    hexagon: { width: 80, height: 80 },
    octagon: { width: 80, height: 80 },
    'dotted-line': { width: 100, height: 10 },
    'dotted-curve': { width: 100, height: 50 }
  }

  const defaults = shapeDefaults[shapeType] || { width: 80, height: 80 }

  // Special colors for certain shapes
  let fillColor = '#ffffff'
  let borderColor = '#000000'

  if (['star', 'heart'].includes(shapeType)) {
    fillColor = '#ff6b6b'
    borderColor = '#d63031'
  } else if (['triangle', 'diamond', 'hexagon', 'octagon', 'polygon'].includes(shapeType)) {
    fillColor = '#ffffff'
    borderColor = '#000000'
  }

  const element = {
    id: generateId(),
    type: 'shape',
    subtype: shapeType,
    x: x - defaults.width / 2, // Center the element
    y: y - defaults.height / 2,
    width: defaults.width,
    height: defaults.height,
    color: fillColor,
    borderColor: borderColor,
    borderWidth: 2,
    rotation: 0
  }

  elements.value.push(element)
  selectedElements.value = [element.id]
}

// Element creation
const addTextElement = (type) => {
  saveState()
  const element = {
    id: generateId(),
    type: 'text',
    subtype: type,
    x: 100,
    y: 100,
    width: 200,
    height: type === 'heading' ? 40 : 30,
    content: type === 'heading' ? 'Heading' : type === 'paragraph' ? 'Paragraph text' : 'Label',
    color: '#000000',
    fontSize: type === 'heading' ? 24 : 16,
    fontWeight: type === 'heading' ? 'bold' : 'normal',
    rotation: 0
  }
  elements.value.push(element)
}

const addShape = (shapeType) => {
  saveState()

  // Shape-specific dimensions and properties
  const shapeDefaults = {
    rectangle: { width: 120, height: 80 },
    square: { width: 100, height: 100 },
    circle: { width: 100, height: 100 },
    oval: { width: 120, height: 80 },
    triangle: { width: 100, height: 100 },
    diamond: { width: 100, height: 100 },
    line: { width: 150, height: 2 },
    arrow: { width: 150, height: 20 },
    curve: { width: 120, height: 60 },
    polygon: { width: 100, height: 100 },
    star: { width: 100, height: 100 },
    heart: { width: 100, height: 100 },
    hexagon: { width: 100, height: 100 },
    octagon: { width: 100, height: 100 },
    parallelogram: { width: 120, height: 80 },
    trapezoid: { width: 120, height: 80 },
    cross: { width: 80, height: 80 },
    'rounded-rect': { width: 120, height: 80 },
    'dotted-line': { width: 150, height: 2 },
    'dotted-curve': { width: 120, height: 60 }
  }

  const defaults = shapeDefaults[shapeType] || { width: 100, height: 100 }

  // Set different colors for different shape types to make them more visible
  let fillColor = '#ffffff'  // Default white
  let borderColor = '#000000' // Default black

  // Special colors for certain shapes to make them more visible
  if (['star', 'heart'].includes(shapeType)) {
    fillColor = '#ff6b6b'  // Red for star and heart
    borderColor = '#d63031'
  } else if (['triangle', 'diamond', 'hexagon', 'octagon', 'polygon'].includes(shapeType)) {
    fillColor = '#ffffff'  // White for line-based shapes
    borderColor = '#000000' // Black border
  } else if (['cross'].includes(shapeType)) {
    fillColor = '#fd79a8'  // Pink for cross
    borderColor = '#e84393'
  }

  const element = {
    id: generateId(),
    type: 'shape',
    subtype: shapeType,
    x: 150,
    y: 150,
    width: defaults.width,
    height: defaults.height,
    color: fillColor,
    borderColor: borderColor,
    borderWidth: 2,
    rotation: 0
  }
  elements.value.push(element)
}

const addIcon = (iconType) => {
  saveState()
  const icons = {
    star: '⭐',
    heart: '❤️',
    check: '✅',
    cross: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    home: '🏠',
    user: '👤',
    mail: '📧',
    phone: '📞',
    location: '📍',
    calendar: '📅',
    clock: '🕐',
    settings: '⚙️',
    search: '🔍',
    download: '⬇️',
    upload: '⬆️',
    save: '💾',
    edit: '✏️',
    delete: '🗑️',
    'arrow-up': '⬆️',
    'arrow-down': '⬇️',
    'arrow-left': '⬅️',
    'arrow-right': '➡️',
    'arrow-up-right': '↗️',
    'arrow-down-right': '↘️',
    'arrow-down-left': '↙️',
    'arrow-up-left': '↖️',
    refresh: '🔄',
    undo: '↩️',
    redo: '↪️',
    forward: '⏩',
    backward: '⏪',
    play: '▶️',
    pause: '⏸️',
    stop: '⏹️',
    plus: '➕',
    minus: '➖',
    multiply: '✖️',
    divide: '➗',
    equals: '🟰',
    question: '❓',
    exclamation: '❗',
    copyright: '©️',
    trademark: '™️',
    registered: '®️',
    dollar: '💲',
    euro: '💶',
    pound: '💷',
    yen: '💴',
    percent: '💯',
    hash: '#️⃣',
    computer: '💻',
    mobile: '📱',
    tablet: '📱',
    camera: '📷',
    printer: '🖨️',
    keyboard: '⌨️',
    mouse: '🖱️',
    monitor: '🖥️',
    speaker: '🔊',
    microphone: '🎤',
    headphones: '🎧',
    book: '📚',
    folder: '📁',
    file: '📄',
    image: '🖼️',
    video: '🎥',
    sun: '☀️',
    moon: '🌙',
    'star-nature': '⭐',
    cloud: '☁️',
    rain: '🌧️',
    snow: '❄️',
    lightning: '⚡',
    fire: '🔥',
    water: '💧',
    earth: '🌍',
    tree: '🌳',
    flower: '🌸',
    leaf: '🍃',
    mountain: '⛰️',
    ocean: '🌊',
    rainbow: '🌈',
    person: '👤',
    people: '👥',
    family: '👨‍👩‍👧‍👦',
    man: '👨',
    woman: '👩',
    child: '🧒',
    baby: '👶',
    elder: '👴',
    'thumbs-up': '👍',
    'thumbs-down': '👎',
    clap: '👏',
    wave: '👋',
    peace: '✌️',
    'ok-hand': '👌',
    pointing: '👉',
    muscle: '💪'
  }
  
  const element = {
    id: generateId(),
    type: 'icon',
    subtype: iconType,
    x: 200,
    y: 200,
    width: 40,
    height: 40,
    content: icons[iconType] || '⭐',
    color: '#000000',
    rotation: 0
  }
  elements.value.push(element)
}

// Icon library functions
const showIconLibrary = () => {
  iconLibraryVisible.value = true
}

const hideIconLibrary = () => {
  iconLibraryVisible.value = false
  customSvgCode.value = ''
}

const addIconFromLibrary = (icon) => {
  addIcon(icon.name)
  hideIconLibrary()
}

const addCustomSvg = () => {
  if (!customSvgCode.value.trim()) return

  saveState()
  const element = {
    id: generateId(),
    type: 'custom-svg',
    x: 150,
    y: 150,
    width: 100,
    height: 100,
    svgCode: customSvgCode.value.trim(),
    rotation: 0
  }
  elements.value.push(element)
  hideIconLibrary()
}

// Media upload functions
const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event) => {
  const file = event.target.files[0]
  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      addImageElement(e.target.result, file.name)
    }
    reader.readAsDataURL(file)
  }
  // Reset input
  event.target.value = ''
}

const addImageFromUrl = () => {
  if (imageUrl.value.trim()) {
    addImageElement(imageUrl.value, 'Image from URL')
    imageUrl.value = ''
  }
}

const addImageElement = (src, name) => {
  saveState()

  // Create a temporary image to get dimensions
  const img = new Image()
  img.onload = () => {
    const maxWidth = 300
    const maxHeight = 300
    let width = img.width
    let height = img.height

    // Scale down if too large
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height)
      width = width * ratio
      height = height * ratio
    }

    const element = {
      id: generateId(),
      type: 'image',
      x: 100,
      y: 100,
      width: Math.round(width),
      height: Math.round(height),
      src: src,
      name: name,
      borderColor: '#000000',
      borderWidth: 0,
      rotation: 0
    }
    elements.value.push(element)
  }

  img.onerror = () => {
    alert('Failed to load image. Please check the URL or file.')
  }

  img.src = src
}

// Selection
const selectElement = (id, event) => {
  if (event.ctrlKey || event.metaKey) {
    if (selectedElements.value.includes(id)) {
      selectedElements.value = selectedElements.value.filter(el => el !== id)
    } else {
      selectedElements.value.push(id)
    }
  } else {
    selectedElements.value = [id]
  }

  // Update color controls based on selected elements
  updateColorControls()
}

const updateColorControls = () => {
  if (selectedElements.value.length > 0) {
    const firstElement = getElementById(selectedElements.value[0])
    if (firstElement) {
      selectedFillColor.value = firstElement.color || '#ffffff'
      selectedBorderColor.value = firstElement.borderColor || '#000000'
      selectedTextColor.value = firstElement.color || '#000000'
    }
  }
}

const updateAllSelectedElements = (property, value) => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element[property] = value
    }
  })
}

const handleCanvasClick = (event) => {
  // Only clear selection if clicking directly on canvas (not on elements)
  if (event.target === canvas.value && !isSelecting.value) {
    selectedElements.value = []
  }
  hideContextMenu()
  // Ensure canvas is focused for keyboard shortcuts
  canvas.value?.focus()
}

const handleCanvasDoubleClick = (event) => {
  if (event.target === canvas.value) {
    // Create a text label at the double-click position
    const rect = canvas.value.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    saveState()
    const element = {
      id: generateId(),
      type: 'text',
      subtype: 'label',
      x: x - 50, // Center the text
      y: y - 15,
      width: 100,
      height: 30,
      content: 'Label',
      color: '#000000',
      fontSize: 16,
      fontWeight: 'normal',
      fontFamily: 'Arial, sans-serif',
      textAlign: 'left',
      rotation: 0
    }
    elements.value.push(element)

    // Select the new element and start editing
    selectedElements.value = [element.id]

    // Start editing the text immediately
    setTimeout(() => {
      startEditing(element.id)
    }, 100)
  }
}

const handleCanvasMouseDown = (event) => {
  // Only start selection if clicking directly on canvas (not on elements) and not dragging
  if (event.target === canvas.value && !event.ctrlKey && !event.metaKey && !isDragging.value) {
    // Start drag selection
    const rect = canvas.value.getBoundingClientRect()
    isSelecting.value = true
    selectionArea.value = {
      active: true,
      startX: event.clientX - rect.left,
      startY: event.clientY - rect.top,
      endX: event.clientX - rect.left,
      endY: event.clientY - rect.top
    }
    hideContextMenu()
    console.log('Started selection at:', selectionArea.value.startX, selectionArea.value.startY)
  }
}

const completeSelection = () => {
  if (!selectionArea.value.active) return

  const minX = Math.min(selectionArea.value.startX, selectionArea.value.endX)
  const maxX = Math.max(selectionArea.value.startX, selectionArea.value.endX)
  const minY = Math.min(selectionArea.value.startY, selectionArea.value.endY)
  const maxY = Math.max(selectionArea.value.startY, selectionArea.value.endY)

  // Only select if the selection area is large enough (avoid accidental selections)
  if (Math.abs(maxX - minX) < 10 || Math.abs(maxY - minY) < 10) {
    selectedElements.value = []
    return
  }

  const selectedIds = []
  elements.value.forEach(element => {
    // Check if element overlaps with selection area
    const elementLeft = element.x
    const elementRight = element.x + element.width
    const elementTop = element.y
    const elementBottom = element.y + element.height

    // Check for overlap (element intersects with selection area)
    if (!(elementRight < minX || elementLeft > maxX ||
          elementBottom < minY || elementTop > maxY)) {
      selectedIds.push(element.id)
    }
  })

  selectedElements.value = selectedIds
  updateColorControls()

  console.log('Selection completed:', selectedIds.length, 'elements selected')
  console.log('Selected element IDs:', selectedIds)
  console.log('Selection area:', { minX, maxX, minY, maxY })
}

// Actions
const duplicateSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  const newElements = []
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const newElement = { ...element, id: generateId(), x: element.x + 20, y: element.y + 20 }
      newElements.push(newElement)
      elements.value.push(newElement)
    }
  })

  selectedElements.value = newElements.map(el => el.id)
}

const rotateSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element.rotation = (element.rotation || 0) + 90
      if (element.rotation >= 360) element.rotation = 0
    }
  })
}

const updateSelectedColors = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      // Update fill color for shapes and icons
      if (element.type === 'shape' || element.type === 'icon') {
        element.color = selectedFillColor.value
      }
      // Update text color for text elements
      if (element.type === 'text') {
        element.color = selectedTextColor.value
      }
      // Update border color for shapes and images
      if (element.type === 'shape' || element.type === 'image') {
        element.borderColor = selectedBorderColor.value
      }
    }
  })
}

const deleteSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  
  elements.value = elements.value.filter(el => !selectedElements.value.includes(el.id))
  selectedElements.value = []
}

const clearAll = () => {
  if (elements.value.length === 0) return
  saveState()
  elements.value = []
  selectedElements.value = []
}

// History
const undo = () => {
  if (!canUndo.value) return
  historyIndex.value--
  elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
  selectedElements.value = []
}

const redo = () => {
  if (!canRedo.value) return
  historyIndex.value++
  elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
  selectedElements.value = []
}

// Project management
const saveProject = () => {
  const project = {
    elements: elements.value,
    canvasBackground: canvasBackground.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(project, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `visual-editor-project-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const triggerLoadProject = () => {
  fileInput.value?.click()
}

const loadProject = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const project = JSON.parse(e.target.result)
      saveState()
      elements.value = project.elements || []
      canvasBackground.value = project.canvasBackground || '#ffffff'
      selectedElements.value = []
    } catch (error) {
      alert('Error loading project file')
    }
  }
  reader.readAsText(file)
}

// Styling functions
const getElementStyle = (element) => {
  const baseStyle = {
    position: 'absolute',
    left: element.x + 'px',
    top: element.y + 'px',
    width: element.width + 'px',
    height: element.height + 'px',
    color: element.color,
    fontSize: element.fontSize ? element.fontSize + 'px' : Math.max(12, Math.min(element.height * 0.6, 48)) + 'px',
    fontWeight: element.fontWeight || 'normal',
    fontFamily: element.fontFamily || 'Arial, sans-serif',
    cursor: element.locked ? 'not-allowed' : 'move',
    userSelect: 'none',
    transform: `rotate(${element.rotation || 0}deg) scale(${zoomLevel.value})`,
    transformOrigin: 'center center',
    opacity: element.opacity !== undefined ? element.opacity : 1,
    display: element.hidden ? 'none' : 'block'
  }

  // Add shadow effect if enabled
  if (element.hasShadow) {
    baseStyle.boxShadow = `${element.shadowOffsetX || 2}px ${element.shadowOffsetY || 2}px ${element.shadowBlur || 4}px ${element.shadowColor || '#000000'}`
  }

  return baseStyle
}

const getCanvasStyle = () => {
  const gridPattern = 'data:image/svg+xml,' + encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
      <rect width="20" height="20" fill="none" stroke="#e0e0e0" stroke-width="0.5"/>
    </svg>
  `)
  
  return {
    background: showGrid.value ? `${canvasBackground.value} url('${gridPattern}')` : canvasBackground.value,
    transform: `scale(${zoomLevel.value})`,
    transformOrigin: 'top left'
  }
}

const getShapeStyle = (element) => {
  const baseStyle = {
    width: '100%',
    height: '100%',
    backgroundColor: element.color,
    border: `${element.borderWidth || 2}px solid ${element.borderColor || element.color}`
  }

  switch (element.subtype) {
    case 'rectangle':
    case 'square':
      return baseStyle
    case 'circle':
      return { ...baseStyle, borderRadius: '50%' }
    case 'oval':
      return { ...baseStyle, borderRadius: '50%' }
    case 'rounded-rect':
      return { ...baseStyle, borderRadius: '15px' }
    case 'triangle':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'diamond':
      return { ...baseStyle, transform: 'rotate(45deg)' }
    case 'line':
      return {
        width: '100%',
        height: `${element.borderWidth || 2}px`,
        backgroundColor: element.borderColor || '#000000',
        border: 'none'
      }
    case 'arrow':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'star':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: `${Math.min(element.width, element.height) * 0.8}px`,
        color: element.color || '#ffffff',
        border: 'none'
      }
    case 'heart':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: `${Math.min(element.width, element.height) * 0.8}px`,
        color: element.color || '#ffffff',
        border: 'none'
      }


    case 'trapezoid':
    case 'hexagon':
    case 'octagon':
    case 'parallelogram':
    case 'cross':
    case 'polygon':
    case 'curve':
    case 'dotted-line':
    case 'dotted-curve':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'cross':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: element.color || '#ffffff',
        border: `${element.borderWidth || 2}px solid ${element.borderColor || '#000000'}`,
        clipPath: 'polygon(40% 0%, 60% 0%, 60% 40%, 100% 40%, 100% 60%, 60% 60%, 60% 100%, 40% 100%, 40% 60%, 0% 60%, 0% 40%, 40% 40%)'
      }
    default:
      return baseStyle
  }
}

const getIconStyle = (element) => {
  return {
    fontSize: Math.min(element.width, element.height) + 'px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%'
  }
}

const getImageStyle = (element) => {
  return {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    border: element.borderWidth ? `${element.borderWidth}px solid ${element.borderColor}` : 'none',
    borderRadius: '4px'
  }
}

const getCustomSvgStyle = (element) => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
}

// Event handlers
const updateMousePosition = (event) => {
  const rect = canvas.value.getBoundingClientRect()
  mousePosition.value = {
    x: Math.round(event.clientX - rect.left),
    y: Math.round(event.clientY - rect.top)
  }
}

const handleMouseMove = (event) => {
  updateMousePosition(event)

  if (isDragging.value && draggedElement.value) {
    const rect = canvas.value.getBoundingClientRect()
    const newX = Math.max(0, event.clientX - rect.left - dragOffset.value.x)
    const newY = Math.max(0, event.clientY - rect.top - dragOffset.value.y)

    // Get the primary dragged element
    const primaryElement = getElementById(draggedElement.value)
    if (primaryElement) {
      const deltaX = newX - primaryElement.x
      const deltaY = newY - primaryElement.y

      // Move all selected elements by the same delta
      selectedElements.value.forEach(id => {
        const element = getElementById(id)
        if (element) {
          element.x = Math.max(0, element.x + deltaX)
          element.y = Math.max(0, element.y + deltaY)
        }
      })

      console.log('Dragging', selectedElements.value.length, 'elements by delta:', deltaX, deltaY)
    }
  } else if (isResizing.value && resizeData.value.elementId) {
    const element = getElementById(resizeData.value.elementId)
    if (element) {
      const rect = canvas.value.getBoundingClientRect()
      const currentX = event.clientX - rect.left
      const currentY = event.clientY - rect.top
      const deltaX = currentX - resizeData.value.startX
      const deltaY = currentY - resizeData.value.startY

      const handle = resizeData.value.handle

      if (handle.includes('e')) { // East (right)
        element.width = Math.max(20, resizeData.value.startWidth + deltaX)
      }
      if (handle.includes('w')) { // West (left)
        const newWidth = Math.max(20, resizeData.value.startWidth - deltaX)
        element.x = resizeData.value.startElementX + (resizeData.value.startWidth - newWidth)
        element.width = newWidth
      }
      if (handle.includes('s')) { // South (bottom)
        element.height = Math.max(20, resizeData.value.startHeight + deltaY)
      }
      if (handle.includes('n')) { // North (top)
        const newHeight = Math.max(20, resizeData.value.startHeight - deltaY)
        element.y = resizeData.value.startElementY + (resizeData.value.startHeight - newHeight)
        element.height = newHeight
      }
    }
  } else if (isRotating.value && rotateData.value.elementId) {
    const element = getElementById(rotateData.value.elementId)
    if (element) {
      const rect = canvas.value.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      const currentAngle = Math.atan2(mouseY - rotateData.value.centerY, mouseX - rotateData.value.centerX) * 180 / Math.PI
      let rotation = currentAngle - rotateData.value.startAngle

      // Normalize rotation to 0-359 degrees
      while (rotation < 0) rotation += 360
      while (rotation >= 360) rotation -= 360

      element.rotation = Math.round(rotation)
    }
  } else if (isSelecting.value) {
    // Update selection area
    const rect = canvas.value.getBoundingClientRect()
    selectionArea.value.endX = event.clientX - rect.left
    selectionArea.value.endY = event.clientY - rect.top
  }
}

const handleMouseUp = (event) => {
  if (isDragging.value || isResizing.value || isRotating.value) {
    saveState()
  }

  // Handle selection completion
  if (isSelecting.value) {
    console.log('Completing selection...')
    completeSelection()
  }

  isDragging.value = false
  isResizing.value = false
  isRotating.value = false
  isSelecting.value = false
  draggedElement.value = null
  resizeData.value = { elementId: null, handle: null, startX: 0, startY: 0, startWidth: 0, startHeight: 0 }
  rotateData.value = { elementId: null, startAngle: 0, centerX: 0, centerY: 0 }
  selectionArea.value = { active: false, startX: 0, startY: 0, endX: 0, endY: 0 }
}

const handleKeyDown = (event) => {
  // Delete/Backspace - Delete selected elements
  if ((event.key === 'Delete' || event.key === 'Backspace') && selectedElements.value.length > 0) {
    event.preventDefault()
    deleteSelected()
  }
  // Escape - Clear selection
  else if (event.key === 'Escape') {
    selectedElements.value = []
    hideContextMenu()
  }
  // Ctrl+Z - Undo
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'z' && !event.shiftKey) {
    event.preventDefault()
    undo()
  }
  // Ctrl+Shift+Z or Ctrl+Y - Redo
  else if ((event.ctrlKey || event.metaKey) && (event.shiftKey && event.key.toLowerCase() === 'z' || event.key.toLowerCase() === 'y')) {
    event.preventDefault()
    redo()
  }
  // Ctrl+C - Copy
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c' && selectedElements.value.length > 0) {
    event.preventDefault()
    copySelected()
  }
  // Ctrl+V - Paste
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'v') {
    event.preventDefault()
    pasteSelected()
  }
  // Ctrl+X - Cut
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'x' && selectedElements.value.length > 0) {
    event.preventDefault()
    cutSelected()
  }
  // Ctrl+D - Duplicate
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'd') {
    event.preventDefault()
    duplicateSelected()
  }
  // Ctrl+A - Select All
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'a') {
    event.preventDefault()
    selectAll()
  }
  // Ctrl+S - Save Project
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 's') {
    event.preventDefault()
    saveProject()
  }
  // Ctrl+G - Group elements
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'g' && !event.shiftKey) {
    event.preventDefault()
    groupElements()
  }
  // Ctrl+Shift+G - Ungroup elements
  else if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key.toLowerCase() === 'g') {
    event.preventDefault()
    ungroupElements()
  }
  // R - Rotate selected elements
  else if (event.key.toLowerCase() === 'r' && selectedElements.value.length > 0) {
    event.preventDefault()
    rotateSelected()
  }
  // G - Toggle grid
  else if (event.key.toLowerCase() === 'g' && !event.ctrlKey && !event.metaKey) {
    event.preventDefault()
    toggleGrid()
  }
  // + - Zoom in
  else if (event.key === '+' || event.key === '=') {
    event.preventDefault()
    zoomIn()
  }
  // - - Zoom out
  else if (event.key === '-') {
    event.preventDefault()
    zoomOut()
  }
  // 0 - Reset zoom
  else if (event.key === '0') {
    event.preventDefault()
    resetZoom()
  }
  // Arrow Keys - Move selected elements
  else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key) && selectedElements.value.length > 0) {
    event.preventDefault()
    moveSelectedElements(event.key, event.shiftKey ? 10 : 1)
  }
}

const startDragging = (id, event) => {
  // Handle multi-select with Ctrl+click first
  if (event.ctrlKey || event.metaKey) {
    event.preventDefault()
    event.stopPropagation()
    // Handle multi-select
    if (selectedElements.value.includes(id)) {
      selectedElements.value = selectedElements.value.filter(el => el !== id)
    } else {
      selectedElements.value.push(id)
    }
    updateColorControls()
    return // Don't start dragging on Ctrl+click
  }

  // Regular single-select and drag
  event.preventDefault()
  event.stopPropagation()

  // Select the element if not already selected
  if (!selectedElements.value.includes(id)) {
    selectedElements.value = [id]
    updateColorControls()
  }

  isDragging.value = true
  draggedElement.value = id
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    dragOffset.value = {
      x: event.clientX - rect.left - element.x,
      y: event.clientY - rect.top - element.y
    }
  }
  hideContextMenu()
  console.log('Started dragging element:', id)
}

const startResizing = (id, handle, event) => {
  event.preventDefault()
  event.stopPropagation()
  isResizing.value = true
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    resizeData.value = {
      elementId: id,
      handle: handle,
      startX: event.clientX - rect.left,
      startY: event.clientY - rect.top,
      startWidth: element.width,
      startHeight: element.height,
      startElementX: element.x,
      startElementY: element.y
    }
  }
  hideContextMenu()
}

const startRotating = (id, event) => {
  event.preventDefault()
  event.stopPropagation()
  isRotating.value = true
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    const centerX = element.x + element.width / 2
    const centerY = element.y + element.height / 2
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * 180 / Math.PI

    rotateData.value = {
      elementId: id,
      startAngle: startAngle - (element.rotation || 0),
      centerX: centerX,
      centerY: centerY
    }
  }
  hideContextMenu()
}

const updateElement = (id) => {
  // Force reactivity update
  const element = getElementById(id)
  if (element) {
    // Trigger reactivity
    elements.value = [...elements.value]
  }
}

// Text editing functions
const startEditing = (id) => {
  const element = getElementById(id)
  if (element && (element.type === 'text' || element.type === 'label' || element.type === 'title')) {
    element.isEditing = true
    // Focus the element after Vue updates
    setTimeout(() => {
      const elementDiv = document.querySelector(`[data-element-id="${id}"]`)
      if (elementDiv) {
        elementDiv.focus()
        // Select all text
        const range = document.createRange()
        range.selectNodeContents(elementDiv)
        const selection = window.getSelection()
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }, 10)
  }
}

const stopEditing = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.isEditing = false
    if (event && event.target) {
      element.content = event.target.textContent || element.content
    }
  }
}

const updateElementText = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.content = event.target.textContent
  }
}

// Helper function to check if element is text-based
const isTextElement = (id) => {
  const element = getElementById(id)
  return element && (element.type === 'text' || element.type === 'label' || element.type === 'title')
}

// Context menu edit function
const editFromContext = () => {
  if (contextMenu.value.elementId) {
    startEditing(contextMenu.value.elementId)
  }
  hideContextMenu()
}

// Context Menu
const hideContextMenu = () => {
  contextMenu.value.visible = false
  contextMenu.value.elementId = null
}

const handleCanvasContextMenu = (event) => {
  event.preventDefault()
  hideContextMenu()
}

const handleElementContextMenu = (id, event) => {
  event.preventDefault()
  event.stopPropagation()

  if (!selectedElements.value.includes(id)) {
    selectedElements.value = [id]
  }

  const rect = canvas.value.getBoundingClientRect()
  contextMenu.value = {
    visible: true,
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
    elementId: id
  }
}

const duplicateFromContext = () => {
  duplicateSelected()
  hideContextMenu()
}

const copyFromContext = () => {
  if (selectedElements.value.length > 0) {
    clipboard.value = selectedElements.value.map(id => {
      const element = getElementById(id)
      return element ? { ...element } : null
    }).filter(Boolean)
  }
  hideContextMenu()
}

const pasteFromContext = () => {
  if (clipboard.value && clipboard.value.length > 0) {
    saveState()
    const newElements = []
    clipboard.value.forEach(elementData => {
      const newElement = {
        ...elementData,
        id: generateId(),
        x: elementData.x + 20,
        y: elementData.y + 20
      }
      newElements.push(newElement)
      elements.value.push(newElement)
    })
    selectedElements.value = newElements.map(el => el.id)
  }
  hideContextMenu()
}

const deleteFromContext = () => {
  deleteSelected()
  hideContextMenu()
}

// Additional shortcut functions
const copySelected = () => {
  if (selectedElements.value.length > 0) {
    clipboard.value = selectedElements.value.map(id => {
      const element = getElementById(id)
      return element ? { ...element } : null
    }).filter(Boolean)
  }
}

const pasteSelected = () => {
  if (clipboard.value && clipboard.value.length > 0) {
    saveState()
    const newElements = []
    clipboard.value.forEach(elementData => {
      const newElement = {
        ...elementData,
        id: generateId(),
        x: elementData.x + 20,
        y: elementData.y + 20
      }
      newElements.push(newElement)
      elements.value.push(newElement)
    })
    selectedElements.value = newElements.map(el => el.id)
  }
}

const cutSelected = () => {
  if (selectedElements.value.length > 0) {
    copySelected()
    deleteSelected()
  }
}

const selectAll = () => {
  selectedElements.value = elements.value.map(el => el.id)
}

const moveSelectedElements = (direction, distance) => {
  if (selectedElements.value.length === 0) return

  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      switch (direction) {
        case 'ArrowUp':
          element.y = Math.max(0, element.y - distance)
          break
        case 'ArrowDown':
          element.y = element.y + distance
          break
        case 'ArrowLeft':
          element.x = Math.max(0, element.x - distance)
          break
        case 'ArrowRight':
          element.x = element.x + distance
          break
      }
    }
  })
}

// Initialize
onMounted(() => {
  saveState()
  canvas.value?.focus()
})
</script>

<style scoped>
.visual-editor-container {
  max-width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
}

.header p {
  font-size: 1.2em;
  opacity: 0.9;
}

/* Ribbon Toolbar Styles */
.ribbon-toolbar {
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  padding: 10px 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ribbon-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.ribbon-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ribbon-label {
  font-size: 11px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.ribbon-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.ribbon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  font-size: 12px;
}

.ribbon-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ribbon-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.ribbon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.ribbon-btn.danger:hover:not(:disabled) {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.ribbon-icon {
  font-size: 16px;
  line-height: 1;
}

.ribbon-text {
  font-size: 10px;
  font-weight: 500;
  color: #495057;
  line-height: 1;
  text-align: center;
}

.ribbon-separator {
  width: 1px;
  height: 40px;
  background: #dee2e6;
  margin: 0 10px;
}

.editor-container {
  display: flex;
  height: 80vh;
}

.toolbar {
  width: 250px;
  background: #2c3e50;
  color: white;
  padding: 20px;
  overflow-y: auto;
}





.toolbar h3 {
  margin-bottom: 15px;
  color: #ecf0f1;
}

.tool-group {
  margin-bottom: 20px;
}

.tool-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  text-transform: uppercase;
  color: #bdc3c7;
}

.btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 5px;
  background: #34495e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:hover:not(:disabled) {
  background: #4a6741;
}

.btn:disabled {
  background: #7f8c8d;
  cursor: not-allowed;
  opacity: 0.6;
}

.btn.danger {
  background: #e74c3c;
}

.btn.danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-icon {
  font-size: 14px;
}

.media-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.url-input-group {
  display: flex;
  gap: 5px;
}

.url-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #2c3e50;
  color: white;
  font-size: 12px;
}

.url-input::placeholder {
  color: #bdc3c7;
}

.url-input:focus {
  outline: none;
  border-color: #3498db;
}

.color-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.color-control {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.color-control label {
  font-size: 11px;
  color: #bdc3c7;
  text-transform: uppercase;
}

.color-input {
  width: 100%;
  height: 30px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #2c3e50;
  cursor: pointer;
}

.color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-input::-webkit-color-swatch {
  border: none;
  border-radius: 3px;
}

.library-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
}

.lib-btn {
  padding: 8px;
  background: #34495e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lib-btn:hover {
  background: #4a6741;
}

.canvas-container {
  flex: 1;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  outline: none;
  cursor: crosshair;
}

.element {
  position: absolute;
  border: 2px solid transparent;
  box-sizing: border-box;
}

.element.selected {
  border-color: #3498db;
  box-shadow: 0 0 0 1px #3498db;
}

.element.text {
  cursor: text;
}

.element.text[contenteditable="true"] {
  outline: 1px dashed #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.resize-handles {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3498db;
  border: 1px solid white;
  pointer-events: all;
  cursor: nw-resize;
}

.resize-handle.nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.mouse-position {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.layers-panel {
  width: 200px;
  background: #34495e;
  color: white;
  padding: 15px;
  overflow-y: auto;
  border-left: 1px solid #2c3e50;
  border-right: 1px solid #2c3e50;
}

.layers-panel h3 {
  margin-bottom: 15px;
  color: #ecf0f1;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.layers-controls {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.layers-controls .btn-small {
  padding: 4px 8px;
  font-size: 10px;
  flex: 1;
}

.layers-list {
  max-height: 400px;
  overflow-y: auto;
}

.layer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  margin-bottom: 2px;
  background: #2c3e50;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.layer-item:hover {
  background: #3a5169;
}

.layer-item.selected {
  background: #3498db;
}

.layer-item.locked {
  opacity: 0.7;
}

.layer-item.hidden {
  opacity: 0.5;
}

.layer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.layer-icon {
  font-size: 14px;
}

.layer-name {
  font-size: 12px;
  color: #ecf0f1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.layer-name[contenteditable="true"] {
  background: #34495e;
  padding: 2px 4px;
  border-radius: 2px;
  outline: 1px solid #3498db;
}

.layer-controls {
  display: flex;
  gap: 4px;
}

.layer-btn {
  background: none;
  border: none;
  color: #bdc3c7;
  cursor: pointer;
  font-size: 12px;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s;
}

.layer-btn:hover {
  background: #34495e;
  color: #ecf0f1;
}

.properties-panel {
  width: 280px;
  background: #34495e;
  color: white;
  padding: 20px;
  overflow-y: auto;
  border-left: 1px solid #2c3e50;
}

.properties-panel h3 {
  margin-bottom: 20px;
  color: #ecf0f1;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.property-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #2c3e50;
}

.property-section:last-child {
  border-bottom: none;
}

.property-section h4 {
  color: #ecf0f1;
  margin-bottom: 15px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.property-group {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #bdc3c7;
}

.property-item {
  margin-bottom: 10px;
}

.property-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 11px;
  text-transform: uppercase;
  color: #bdc3c7;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-input-large {
  width: 50px;
  height: 35px;
  border: 1px solid #2c3e50;
  border-radius: 6px;
  background: #2c3e50;
  cursor: pointer;
}

.color-input-large::-webkit-color-swatch-wrapper {
  padding: 2px;
}

.color-input-large::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

.color-label {
  font-size: 12px;
  color: #95a5a6;
  flex: 1;
}

.btn-small {
  padding: 6px 10px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.3s;
}

.btn-small:hover {
  background: #2980b9;
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid #2c3e50;
  border-radius: 4px;
  font-size: 12px;
  background: #2c3e50;
  color: white;
}

.input-group input:focus {
  outline: none;
  border-color: #3498db;
  background: #34495e;
}

.input-group input::placeholder {
  color: #95a5a6;
}

/* Icon Library Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: #34495e;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2c3e50;
  background: #2c3e50;
}

.modal-header h3 {
  color: #ecf0f1;
  margin: 0;
  font-size: 18px;
}

.modal-close {
  background: none;
  border: none;
  color: #bdc3c7;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.modal-close:hover {
  background: #34495e;
  color: #ecf0f1;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 70vh;
}

.icon-categories {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  background: #2c3e50;
  color: #bdc3c7;
  border: 1px solid #34495e;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 12px;
}

.category-btn:hover {
  background: #34495e;
  color: #ecf0f1;
}

.category-btn.active {
  background: #3498db;
  color: white;
  border-color: #2980b9;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 10px;
  margin-bottom: 30px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #2c3e50;
  border-radius: 4px;
  background: #2c3e50;
}

.icon-item {
  width: 60px;
  height: 60px;
  background: #34495e;
  border: 1px solid #2c3e50;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s;
}

.icon-item:hover {
  background: #3498db;
  border-color: #2980b9;
  transform: scale(1.05);
}

.custom-svg-section {
  border-top: 1px solid #2c3e50;
  padding-top: 20px;
}

.custom-svg-section h4 {
  color: #ecf0f1;
  margin-bottom: 15px;
  font-size: 16px;
}

.svg-textarea {
  width: 100%;
  height: 120px;
  background: #2c3e50;
  border: 1px solid #34495e;
  border-radius: 4px;
  color: white;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
  margin-bottom: 15px;
}

.svg-textarea:focus {
  outline: none;
  border-color: #3498db;
}

.svg-textarea::placeholder {
  color: #95a5a6;
}

/* Font Controls */
.font-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #2c3e50;
  border-radius: 4px;
  background: #2c3e50;
  color: white;
  font-size: 12px;
}

.font-select:focus {
  outline: none;
  border-color: #3498db;
  background: #34495e;
}

.unit-label {
  font-size: 12px;
  color: #95a5a6;
  margin-left: 5px;
}

.opacity-slider {
  flex: 1;
  -webkit-appearance: none;
  height: 6px;
  border-radius: 5px;
  background: #2c3e50;
  outline: none;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.range-input {
  flex: 1;
  -webkit-appearance: none;
  height: 6px;
  border-radius: 5px;
  background: #2c3e50;
  outline: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.range-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.shadow-controls {
  margin-top: 10px;
  padding-left: 20px;
}

.ribbon-btn.active {
  background: #3498db;
  color: white;
  border-color: #2980b9;
}

.property-item input[type="color"] {
  width: 100%;
  height: 30px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  cursor: pointer;
}

.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 5px;
}

/* Context Menu */
.context-menu {
  position: absolute;
  background: white;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 120px;
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background: #ecf0f1;
}

.context-menu-item.danger {
  color: #e74c3c;
}

.context-menu-item.danger:hover {
  background: #fadbd8;
}

.context-menu-separator {
  height: 1px;
  background: #bdc3c7;
  margin: 4px 0;
}

/* Improved element selection */
.element {
  transition: none;
}

.element:hover {
  outline: 1px dashed #3498db;
}

.element.selected:hover {
  outline: none;
}

/* Better resize handles */
.resize-handle {
  border-radius: 1px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.resize-handle:hover {
  background: #2980b9;
  transform: scale(1.2);
}

.rotate-handle {
  position: absolute;
  width: 24px;
  height: 24px;
  background: #e74c3c;
  border: 2px solid white;
  border-radius: 50%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  z-index: 1002;
  pointer-events: auto;
}

.rotate-top {
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
}

.rotate-bottom {
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
}

.rotate-handle:hover {
  background: #c0392b;
  transform: translateX(-50%) scale(1.2);
}

.rotate-handle:active {
  cursor: grabbing;
}

/* Selection Area */
.selection-area {
  position: absolute;
  border: 2px dashed #3498db;
  background: rgba(52, 152, 219, 0.1);
  pointer-events: none;
  z-index: 999;
}


</style>
