<template>
  <div class="jeecg-visual-editor">
    <div class="editor-header">
      <h2>🎨 Vue可视化编辑器</h2>
      <p>拖拽式可视化设计工具，支持文本、形状、图标和图像编辑</p>
    </div>

    <!-- Microsoft Office Ribbon风格工具栏 -->
    <div class="ribbon-toolbar">
      <!-- 文件组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          文件
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-button-group">
            <a-button @click="newProject" size="small" title="新建" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                <path d="M11 13h2v2h-2zm0-4h2v2h-2z"/>
              </svg>
            </a-button>
            <a-button @click="importProject" size="small" title="导入" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
              </svg>
            </a-button>
            <a-button @click="saveProject" size="small" title="保存 (Ctrl+S)" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
              </svg>
            </a-button>
                          <a-dropdown>
                <a-button size="small" title="导出" class="icon-only-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M23,12L19,8V11H10V13H19V16M1,18V6C1,4.89 1.89,4 3,4H15A2,2 0 0,1 17,6V9H15V6H3V18H15V15H17V18A2,2 0 0,1 15,20H3C1.89,20 1,19.1 1,18Z"/>
                  </svg>
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style="margin-left: 4px;">
                    <path d="M7,10L12,15L17,10H7Z"/>
                  </svg>
                </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="exportAsSVG">
                    <FileImageOutlined /> 导出为SVG
                  </a-menu-item>
                  <a-menu-item @click="exportAsPNG">
                    <PictureOutlined /> 导出为PNG
                  </a-menu-item>
                  <a-menu-item @click="exportAsJSON">
                    <FileTextOutlined /> 导出为JSON
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>

      <!-- 编辑组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
          </svg>
          编辑
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-button-group">
            <a-button @click="undo" :disabled="!canUndo" size="small" title="撤销 (Ctrl+Z)" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12.5,8C9.85,8 7.45,9 5.6,10.6L2,7V16H11L7.38,12.38C8.77,11.22 10.54,10.5 12.5,10.5C16.04,10.5 19.05,12.81 20.1,16L22.47,15.22C21.08,11.03 17.15,8 12.5,8Z"/>
              </svg>
            </a-button>
            <a-button @click="redo" :disabled="!canRedo" size="small" title="重做 (Ctrl+Y)" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.4,10.6C16.55,9 14.15,8 11.5,8C6.85,8 2.92,11.03 1.53,15.22L3.9,16C4.95,12.81 7.96,10.5 11.5,10.5C13.46,10.5 15.23,11.22 16.62,12.38L13,16H22V7L18.4,10.6Z"/>
              </svg>
            </a-button>
          </div>
          <div class="ribbon-button-group">
            <a-button @click="duplicateSelected" :disabled="selectedElements.length === 0" size="small" title="复制 (Ctrl+D)" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
              </svg>
            </a-button>
            <a-button danger @click="deleteSelected" :disabled="selectedElements.length === 0" size="small" title="删除 (Del)" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 文本元素组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M5,4V7H10.5V19H13.5V7H19V4H5Z"/>
          </svg>
          文本
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-button-group">
            <a-button @click="addTextElement('heading')" @dragstart="startDragFromRibbon('text-heading', $event)" draggable="true" size="small" title="标题" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M5,4V7H10.5V19H13.5V7H19V4H5Z"/>
              </svg>
            </a-button>
            <a-button @click="addTextElement('paragraph')" @dragstart="startDragFromRibbon('text-paragraph', $event)" draggable="true" size="small" title="段落" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z"/>
              </svg>
            </a-button>
            <a-button @click="addTextElement('label')" @dragstart="startDragFromRibbon('text-label', $event)" draggable="true" size="small" title="标签" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M5.5,7A1.5,1.5 0 0,1 4,5.5A1.5,1.5 0 0,1 5.5,4A1.5,1.5 0 0,1 7,5.5A1.5,1.5 0 0,1 5.5,7M21.41,11.58L12.41,2.58C12.05,2.22 11.55,2 11,2H4C2.89,2 2,2.89 2,4V11C2,11.55 2.22,12.05 2.59,12.41L11.58,21.41C11.95,21.77 12.45,22 13,22C13.55,22 14.05,21.77 14.41,21.41L21.41,14.41C21.78,14.05 22,13.55 22,13C22,12.44 21.77,11.94 21.41,11.58Z"/>
              </svg>
            </a-button>
          </div>
        </div>
      </div>


      <!-- 基础形状组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16,8A8,8 0 0,1 8,16A8,8 0 0,1 0,8A8,8 0 0,1 8,0A8,8 0 0,1 16,8M18.5,10C18.5,10 16.5,8 16.5,8C16.5,8 14.5,10 14.5,10C14.5,10 16.5,12 16.5,12C16.5,12 18.5,10 18.5,10M7.5,14C7.5,14 9.5,16 9.5,16C9.5,16 11.5,14 11.5,14C11.5,14 9.5,12 9.5,12C9.5,12 7.5,14 7.5,14Z"/>
          </svg>
          基础形状
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-shapes-grid">
            <a-button class="shape-btn" @click="addShape('rectangle')" @dragstart="startDragFromRibbon('rectangle', $event)" draggable="true" size="small" title="矩形">▭</a-button>
            <a-button class="shape-btn" @click="addShape('circle')" @dragstart="startDragFromRibbon('circle', $event)" draggable="true" size="small" title="圆形">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="9"/>
              </svg>
            </a-button>
            <a-button class="shape-btn" @click="addShape('triangle')" @dragstart="startDragFromRibbon('triangle', $event)" draggable="true" size="small" title="三角形">△</a-button>
            <a-button class="shape-btn" @click="addShape('diamond')" @dragstart="startDragFromRibbon('diamond', $event)" draggable="true" size="small" title="菱形">◇</a-button>
            <a-button class="shape-btn" @click="addShape('line')" @dragstart="startDragFromRibbon('line', $event)" draggable="true" size="small" title="直线">─</a-button>
            <a-button class="shape-btn" @click="addShape('arrow')" @dragstart="startDragFromRibbon('arrow', $event)" draggable="true" size="small" title="箭头">→</a-button>
          </div>
        </div>
      </div>

      <!-- 数学符号形状组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M12,8L7.91,12.09L9.33,13.5L12,10.83L14.67,13.5L16.09,12.09L12,8M12,16L16.09,11.91L14.67,10.5L12,13.17L9.33,10.5L7.91,11.91L12,16Z"/>
          </svg>
          数学符号
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-math-symbols-compact">
            <!-- 第一行：基础运算符号 -->
            <div class="math-row">
              <a-button class="shape-btn-small" @click="addMathShape('plus')" @dragstart="startDragFromRibbon('math-plus', $event)" draggable="true" size="small" title="加号">+</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('minus')" @dragstart="startDragFromRibbon('math-minus', $event)" draggable="true" size="small" title="减号">−</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('multiply')" @dragstart="startDragFromRibbon('math-multiply', $event)" draggable="true" size="small" title="乘号">×</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('divide')" @dragstart="startDragFromRibbon('math-divide', $event)" draggable="true" size="small" title="除号">÷</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('equals')" @dragstart="startDragFromRibbon('math-equals', $event)" draggable="true" size="small" title="等号">=</a-button>
            </div>
            
            <!-- 第二行：比较符号 -->
            <div class="math-row">
              <a-button class="shape-btn-small" @click="addMathShape('less-than')" @dragstart="startDragFromRibbon('math-less-than', $event)" draggable="true" size="small" title="小于"><</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('greater-than')" @dragstart="startDragFromRibbon('math-greater-than', $event)" draggable="true" size="small" title="大于">></a-button>
              <a-button class="shape-btn-small" @click="addMathShape('less-equal')" @dragstart="startDragFromRibbon('math-less-equal', $event)" draggable="true" size="small" title="小于等于">≤</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('greater-equal')" @dragstart="startDragFromRibbon('math-greater-equal', $event)" draggable="true" size="small" title="大于等于">≥</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('not-equal')" @dragstart="startDragFromRibbon('math-not-equal', $event)" draggable="true" size="small" title="不等于">≠</a-button>
            </div>
            
            <!-- 第三行：特殊符号 -->
            <div class="math-row">
              <a-button class="shape-btn-small math-sqrt-btn" @click="addMathShape('sqrt')" @dragstart="startDragFromRibbon('math-sqrt', $event)" draggable="true" size="small" title="根号">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3,18 L6,12 L9,18 L12,6 L21,6"/>
                </svg>
              </a-button>
              <a-button class="shape-btn-small" @click="addMathShape('pi')" @dragstart="startDragFromRibbon('math-pi', $event)" draggable="true" size="small" title="圆周率">π</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('infinity')" @dragstart="startDragFromRibbon('math-infinity', $event)" draggable="true" size="small" title="无穷">∞</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('plus-minus')" @dragstart="startDragFromRibbon('math-plus-minus', $event)" draggable="true" size="small" title="正负">±</a-button>
              <a-button class="shape-btn-small" @click="addMathShape('division-bracket')" @dragstart="startDragFromRibbon('math-division-bracket', $event)" draggable="true" size="small" title="长除法">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M 2,20 Q 2,12 2,6 Q 2,4 6,4 L 22,4"/>
                  <line x1="6" y1="4" x2="6" y2="20"/>
                </svg>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 高级形状组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.6,5.5L11,1L6.4,5.5L11,10M11,23L15.6,18.5L11,14L6.4,18.5M23,11L18.5,6.4L14,11L18.5,15.6M1,11L5.5,15.6L10,11L5.5,6.4"/>
          </svg>
          高级形状
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-shapes-grid">
            <a-button class="shape-btn" @click="addShape('curve')" @dragstart="startDragFromRibbon('curve', $event)" draggable="true" size="small" title="曲线">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3,12 Q8,6 12,12 T21,12"/>
              </svg>
            </a-button>
            <a-button class="shape-btn" @click="addShape('dotted-line')" @dragstart="startDragFromRibbon('dotted-line', $event)" draggable="true" size="small" title="虚线">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="3" y1="12" x2="21" y2="12" stroke-dasharray="4,4"/>
              </svg>
            </a-button>
            <a-button class="shape-btn" @click="addShape('dotted-curve')" @dragstart="startDragFromRibbon('dotted-curve', $event)" draggable="true" size="small" title="虚线曲线">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3,12 Q8,6 12,12 T21,12" stroke-dasharray="4,4"/>
              </svg>
            </a-button>
            <a-button class="shape-btn" @click="addShape('hexagon')" @dragstart="startDragFromRibbon('hexagon', $event)" draggable="true" size="small" title="六边形">⬡</a-button>
            <a-button class="shape-btn" @click="addShape('octagon')" @dragstart="startDragFromRibbon('octagon', $event)" draggable="true" size="small" title="八边形">⯃</a-button>
            <a-button class="shape-btn" @click="addShape('parallelogram')" @dragstart="startDragFromRibbon('parallelogram', $event)" draggable="true" size="small" title="平行四边形">▱</a-button>
            <a-button class="shape-btn" @click="addShape('trapezoid')" @dragstart="startDragFromRibbon('trapezoid', $event)" draggable="true" size="small" title="梯形">⏢</a-button>
            <a-button class="shape-btn" @click="addShape('cross')" @dragstart="startDragFromRibbon('cross', $event)" draggable="true" size="small" title="十字">✚</a-button>
            <a-button class="shape-btn" @click="addShape('rounded-rect')" @dragstart="startDragFromRibbon('rounded-rect', $event)" draggable="true" size="small" title="圆角矩形">▢</a-button>
          </div>
        </div>
      </div>

      <!-- 图像组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
          </svg>
          图像
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-button-group">
            <a-button @click="triggerImageUpload" size="small" title="上传图片" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z"/>
              </svg>
            </a-button>
            <a-button @click="showImageUrlDialog" size="small" title="URL图片" class="icon-only-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10.59,13.41C11,13.8 11,14.4 10.59,14.81C10.2,15.2 9.6,15.2 9.19,14.81L7.77,13.39L7.77,13.39L6.36,12L7.77,10.61L9.19,12.03C9.6,12.42 10.2,12.42 10.59,12.03C11,11.64 11,11.04 10.59,10.63L9.17,9.21L9.17,9.21L7.05,7.05C6.64,6.64 6.04,6.64 5.63,7.05L3.51,9.17C3.12,9.56 3.12,10.16 3.51,10.57L5.63,12.69L5.63,12.69L7.05,14.11C7.46,14.5 8.06,14.5 8.47,14.11L9.19,13.39L10.59,13.41M13.41,9.19C13.8,8.8 14.4,8.8 14.81,9.19L16.23,10.61L16.23,10.61L17.64,12L16.23,13.39L14.81,11.97C14.4,11.58 13.8,11.58 13.41,11.97C13,12.36 13,12.96 13.41,13.37L14.83,14.79L14.83,14.79L16.95,16.95C17.36,17.36 17.96,17.36 18.37,16.95L20.49,14.83C20.88,14.44 20.88,13.84 20.49,13.43L18.37,11.31L18.37,11.31L16.95,9.89C16.54,9.5 15.94,9.5 15.53,9.89L14.81,10.61L13.41,9.19Z"/>
              </svg>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 图标组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
          </svg>
          图标
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-icons-grid">
            <a-button class="icon-btn" @click="addIcon('star')" @dragstart="startDragFromRibbon('icon-star', $event)" draggable="true" size="small" title="星星">⭐</a-button>
            <a-button class="icon-btn" @click="addIcon('heart')" @dragstart="startDragFromRibbon('icon-heart', $event)" draggable="true" size="small" title="心形">❤️</a-button>
            <a-button class="icon-btn" @click="addIcon('check')" @dragstart="startDragFromRibbon('icon-check', $event)" draggable="true" size="small" title="勾选">✅</a-button>
            <a-button class="icon-btn" @click="addIcon('warning')" @dragstart="startDragFromRibbon('icon-warning', $event)" draggable="true" size="small" title="警告">⚠️</a-button>
            <a-button class="icon-btn" @click="addIcon('home')" @dragstart="startDragFromRibbon('icon-home', $event)" draggable="true" size="small" title="主页">🏠</a-button>
            <a-button class="icon-btn" @click="addIcon('user')" @dragstart="startDragFromRibbon('icon-user', $event)" draggable="true" size="small" title="用户">👤</a-button>
            <a-button class="icon-btn" @click="addIcon('mail')" @dragstart="startDragFromRibbon('icon-mail', $event)" draggable="true" size="small" title="邮件">📧</a-button>
            <a-button class="icon-btn" @click="addIcon('phone')" @dragstart="startDragFromRibbon('icon-phone', $event)" draggable="true" size="small" title="电话">📞</a-button>
            <a-button class="icon-btn" @click="showIconLibrary" size="small" title="更多图标">➕</a-button>
          </div>
        </div>
      </div>

      <!-- 对齐组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11,2H13V7H21V9H13V11H19V13H13V15H17V17H13V22H11V17H7V15H11V13H5V11H11V9H3V7H11V2Z"/>
          </svg>
          对齐
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-compact-grid">
            <!-- 第一行 -->
            <div class="compact-row">
              <a-button @click="alignLeft" :disabled="selectedElements.length < 2" size="small" title="左对齐" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3,3H5V21H3V3M7,7H21V9H7V7M7,11H19V13H7V11M7,15H17V17H7V15Z"/>
                </svg>
              </a-button>
              <a-button @click="alignCenter" :disabled="selectedElements.length < 2" size="small" title="居中对齐" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M11,2H13V7H21V9H13V11H19V13H13V15H17V17H13V22H11V17H7V15H11V13H5V11H11V9H3V7H11V2Z"/>
                </svg>
              </a-button>
              <a-button @click="alignRight" :disabled="selectedElements.length < 2" size="small" title="右对齐" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,3H21V21H19V3M3,7H17V9H3V7M5,11H17V13H5V11M7,15H17V17H7V15Z"/>
                </svg>
              </a-button>
            </div>
            <!-- 第二行 -->
            <div class="compact-row">
              <a-button @click="rotateSelected" :disabled="selectedElements.length === 0" size="small" title="旋转 (R)" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z"/>
                </svg>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视图组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-header">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.5,14H20.5L17.5,17L15.5,14M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
          </svg>
          视图
        </div>
        <div class="ribbon-group-content">
          <div class="ribbon-compact-grid">
            <!-- 第一行 -->
            <div class="compact-row">
              <a-button @click="zoomIn" size="small" title="放大 (+)" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5,14H20.5L17.5,17L15.5,14M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5M12,9H10V7H9V9H7V10H9V12H10V10H12V9Z"/>
                </svg>
              </a-button>
              <a-button @click="zoomOut" size="small" title="缩小 (-)" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5,14H20.5L17.5,17L15.5,14M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5M7,9V10H12V9H7Z"/>
                </svg>
              </a-button>
              <a-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'" size="small" title="网格" class="compact-btn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8,8H16V16H8V8M6,6V18H18V6H6M4,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4Z"/>
                </svg>
              </a-button>
            </div>
            <!-- 第二行 -->
            <div class="compact-row">
              <a-select 
                v-model:value="zoomLevel" 
                size="small" 
                style="width: 68px;"
                @change="setZoomLevel"
                class="compact-select"
              >
                <a-select-option :value="0.5">50%</a-select-option>
                <a-select-option :value="0.75">75%</a-select-option>
                <a-select-option :value="1">100%</a-select-option>
                <a-select-option :value="1.25">125%</a-select-option>
                <a-select-option :value="1.5">150%</a-select-option>
              </a-select>
              <a-dropdown>
                <a-button size="small" title="背景设置" class="compact-btn">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
                  </svg>
                  <svg width="10" height="10" viewBox="0 0 24 24" fill="currentColor" style="margin-left: 2px;">
                    <path d="M7,10L12,15L17,10H7Z"/>
                  </svg>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="color">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <span>背景颜色:</span>
                        <input type="color" v-model="canvasBackground" @change="updateCanvasBackground" style="width: 40px; height: 24px; border: none; cursor: pointer;" />
                      </div>
                    </a-menu-item>
                    <a-menu-item key="image" @click="selectBackgroundImage">
                      📁 上传背景图片
                    </a-menu-item>
                    <a-menu-item key="url" @click="showBackgroundUrlDialog">
                      🔗 背景图片链接
                    </a-menu-item>
                    <a-menu-item key="clear" @click="clearBackground">
                      🗑️ 清除背景
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="editor-main">
      <!-- 画布容器 -->
      <div class="canvas-container">
        <div
          class="canvas"
          ref="canvas"
          @click="handleCanvasClick"
          @dblclick="handleCanvasDoubleClick"
          @mousedown="handleCanvasMouseDown"
          @mousemove="handleCanvasMouseMove"
          @mouseup="handleCanvasMouseUp"
          @keydown="handleKeyDown"
          @contextmenu="handleCanvasContextMenu"
          @dragover.prevent
          @drop="handleCanvasDrop"

          tabindex="0"
          :style="{
            transform: `scale(${zoomLevel})`,
            backgroundColor: canvasBackground,
            backgroundImage: getCanvasBackgroundImage(),
            backgroundSize: canvasBackgroundImage ? 'cover' : (showGrid ? '20px 20px' : 'auto'),
            backgroundRepeat: canvasBackgroundImage ? 'no-repeat' : 'repeat',
            backgroundPosition: 'center'
          }"
        >
          <!-- Elements -->
          <div
            v-for="element in elements"
            :key="element.id"
            :class="['element', element.type, { selected: selectedElements.includes(element.id) }]"
            :style="getElementStyle(element)"
            @click.stop="selectElement(element.id, $event)"
            @mousedown.stop="startDragging(element.id, $event)"
            @contextmenu.prevent="handleElementContextMenu(element.id, $event)"
            :title="`Element: ${element.type} (${element.id})`"
          >
            <!-- Text Elements -->
            <div v-if="element.type === 'text'"
                 :data-element-id="element.id"
                 :contenteditable="element.isEditing || selectedElements.includes(element.id)"
                 @blur="stopEditing(element.id, $event)"
                 @dblclick.stop="startEditing(element.id)"
                 @keydown.enter.prevent="stopEditing(element.id, $event)"
                 v-html="element.content">
            </div>
            
            <!-- Shape Elements -->
            <div v-else-if="element.type === 'shape'"
                 :style="getShapeStyle(element)">
              <span v-if="element.subtype === 'star'">⭐</span>
              <span v-else-if="element.subtype === 'heart'">♥</span>

              <!-- 手绘形状 -->
              <svg v-else-if="element.subtype === 'freehand-circle'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <circle cx="50" cy="50" r="40"
                        fill="none"
                        :stroke="element.borderColor || '#000000'"
                        :stroke-width="(element.borderWidth || 2) * 2"
                        stroke-dasharray="3,3"
                        vector-effect="non-scaling-stroke"/>
              </svg>

              <svg v-else-if="element.subtype === 'freehand-rect'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <rect x="10" y="10" width="80" height="80"
                      fill="none"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-dasharray="3,3"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <svg v-else-if="element.subtype === 'freehand-line'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <line x1="10" y1="50" x2="90" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-dasharray="3,3"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <svg v-else-if="element.subtype === 'freehand-curve'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <path d="M 10,50 Q 30,20 50,50 T 90,50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      fill="none"
                      stroke-dasharray="3,3"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <svg v-else-if="element.subtype === 'freehand-arrow'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <line x1="10" y1="50" x2="75" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-dasharray="3,3"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
                <polyline points="70,40 85,50 70,60"
                          fill="none"
                          :stroke="element.borderColor || '#000000'"
                          :stroke-width="(element.borderWidth || 2) * 2"
                          stroke-dasharray="3,3"
                          stroke-linejoin="round"
                          vector-effect="non-scaling-stroke"/>
              </svg>

              <svg v-else-if="element.subtype === 'freehand-star'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="50,10 60,35 85,35 67,55 75,80 50,65 25,80 33,55 15,35 40,35"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-dasharray="3,3"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for arrow -->
              <svg v-else-if="element.subtype === 'arrow'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <!-- Arrow line -->
                <line x1="10" y1="50" x2="75" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
                <!-- Arrow head -->
                <polygon points="75,40 90,50 75,60"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>
              <!-- SVG for triangle -->
              <svg v-else-if="element.subtype === 'triangle'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="50,5 5,95 95,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>
              <!-- SVG for trapezoid -->
              <svg v-else-if="element.subtype === 'trapezoid'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="20,5 80,5 95,95 5,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for hexagon -->
              <svg v-else-if="element.subtype === 'hexagon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="25,5 75,5 95,35 75,95 25,95 5,35"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for octagon -->
              <svg v-else-if="element.subtype === 'octagon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="30,10 70,10 90,30 90,70 70,90 30,90 10,70 10,30"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for parallelogram -->
              <svg v-else-if="element.subtype === 'parallelogram'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="20,5 95,5 80,95 5,95"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for cross -->
              <svg v-else-if="element.subtype === 'cross'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="35,5 65,5 65,35 95,35 95,65 65,65 65,95 35,95 35,65 5,65 5,35 35,35"
                         :fill="element.color || '#ffffff'"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for polygon -->
              <svg v-else-if="element.subtype === 'polygon'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <polygon points="50,5 85,25 85,75 50,95 15,75 15,25"
                         fill="none"
                         :stroke="element.borderColor || '#000000'"
                         :stroke-width="(element.borderWidth || 2) * 2"
                         stroke-linejoin="round"
                         vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for curve -->
              <svg v-else-if="element.subtype === 'curve'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <path d="M 10,80 Q 50,20 90,80"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      fill="none"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for dotted line -->
              <svg v-else-if="element.subtype === 'dotted-line'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <line x1="10" y1="50" x2="90" y2="50"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      stroke-dasharray="5,5"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>

              <!-- SVG for dotted curve -->
              <svg v-else-if="element.subtype === 'dotted-curve'"
                   width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 100 100"
                   preserveAspectRatio="none">
                <path d="M 10,80 Q 50,20 90,80"
                      :stroke="element.borderColor || '#000000'"
                      :stroke-width="(element.borderWidth || 2) * 2"
                      fill="none"
                      stroke-dasharray="5,5"
                      stroke-linecap="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>
            </div>
            
            <!-- Icon Elements -->
            <div v-else-if="element.type === 'icon'"
                 :style="getIconStyle(element)">
              {{ element.content }}
            </div>

            <!-- Image Elements -->
            <img v-else-if="element.type === 'image'"
                 :src="element.src"
                 :alt="element.name"
                 :style="getImageStyle(element)"
                 draggable="false" />

            <!-- Custom SVG Elements -->
            <div v-else-if="element.type === 'custom-svg'"
                 :style="getCustomSvgStyle(element)"
                 v-html="element.svgCode">
            </div>

            <!-- Freehand Drawing Elements -->
            <div v-else-if="element.type === 'freehand-drawing'"
                 :style="getFreehandDrawingStyle(element)">
              <svg width="100%" height="100%"
                   style="position: absolute; top: 0; left: 0;"
                   viewBox="0 0 200 200"
                   preserveAspectRatio="none">
                <path :d="element.path"
                      fill="none"
                      :stroke="element.strokeColor || '#000000'"
                      :stroke-width="element.strokeWidth || 2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      vector-effect="non-scaling-stroke"/>
              </svg>
            </div>

            <!-- Math Shape Elements -->
            <div v-else-if="element.type === 'math-shape'"
                 :style="getMathShapeStyle(element)">
              <div v-if="element.isDivisionBracket" class="division-bracket-container">
                <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
                  <!-- 除法符号：弧形连接到水平线 -->
                  <path d="M 5,85 Q 5,50 5,25 Q 5,15 15,15 L 95,15" 
                        :stroke="element.color || '#000000'" 
                        stroke-width="2" 
                        fill="none"/>
                  <!-- 垂直线 -->
                  <line x1="15" y1="15" x2="15" y2="95" 
                        :stroke="element.color || '#000000'" 
                        stroke-width="2"/>
                </svg>
              </div>
              <div v-else class="math-shape-content">
                <svg v-if="element.symbol === '√'" width="100%" height="100%" :viewBox="`0 0 ${Math.max(100, element.width * 1.25)} 100`" preserveAspectRatio="none">
                  <!-- 左边勾部分 - 固定形状 -->
                  <path d="M10,80 L25,50 L35,80 L45,20" 
                        :stroke="element.color || '#000000'" 
                        stroke-width="2" 
                        fill="none"
                        vector-effect="non-scaling-stroke"/>
                  <!-- 上划线部分 - 随宽度拉伸 -->
                  <line x1="45" y1="20" :x2="Math.max(90, element.width * 1.1)" y2="20" 
                        :stroke="element.color || '#000000'" 
                        stroke-width="2" 
                        vector-effect="non-scaling-stroke"/>
                </svg>
                <span v-else class="math-symbol-text" :style="{ 
                  fontFamily: element.symbol === 'π' || element.symbol === '∞' || element.symbol === '±' ? 'Times New Roman, serif' : 'inherit',
                  fontSize: Math.min(element.width * 0.8, element.height * 0.8) + 'px',
                  lineHeight: element.height + 'px'
                }">
                  {{ element.symbol }}
                </span>
              </div>
            </div>

            <!-- Resize Handles -->
            <div v-if="selectedElements.includes(element.id)" class="resize-handles">
              <div class="resize-handle nw" @mousedown.stop="startResizing(element.id, 'nw', $event)"></div>
              <div class="resize-handle ne" @mousedown.stop="startResizing(element.id, 'ne', $event)"></div>
              <div class="resize-handle sw" @mousedown.stop="startResizing(element.id, 'sw', $event)"></div>
              <div class="resize-handle se" @mousedown.stop="startResizing(element.id, 'se', $event)"></div>
              <!-- Rotation Handle - 只保留一个 -->
              <div class="rotate-handle rotate-top" @mousedown.stop="startRotating(element.id, $event)" title="拖拽旋转元素">🔄</div>
            </div>
          </div>



          <!-- Selection Area -->
          <div v-if="selectionArea.active"
               class="selection-area"
               :style="{
                 left: Math.min(selectionArea.startX, selectionArea.endX) + 'px',
                 top: Math.min(selectionArea.startY, selectionArea.endY) + 'px',
                 width: Math.abs(selectionArea.endX - selectionArea.startX) + 'px',
                 height: Math.abs(selectionArea.endY - selectionArea.startY) + 'px'
               }">
          </div>

          <!-- Context Menu -->
          <div
            v-if="contextMenu.visible"
            class="context-menu"
            :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
            @click.stop
          >
            <div v-if="isTextElement(contextMenu.elementId)" class="context-menu-item" @click="editFromContext">
              ✏️ Edit
            </div>
            <div class="context-menu-item" @click="duplicateFromContext">
              📋 Duplicate
            </div>
            <div class="context-menu-item" @click="copyFromContext">
              📄 Copy
            </div>
            <div class="context-menu-item" @click="pasteFromContext">
              📋 Paste
            </div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item danger" @click="deleteFromContext">
              🗑️ Delete
            </div>
          </div>
        </div>
      </div>

      <!-- Properties Panel -->
      <div class="properties-panel" v-if="selectedElements.length > 0">
        <h3>属性设置 ({{ selectedElements.length }} 个元素)</h3>

        <!-- Text Properties - 移到最前面 -->
        <div v-if="hasTextElements" class="property-section">
          <h4>文本属性</h4>

          <!-- 数学符号面板 - 移到最上面 -->
          <div class="property-item">
            <label>数学符号</label>
            <div class="math-symbols-panel">
              <button class="math-symbol-btn sqrt-btn" @click="insertMathSymbol('√')" title="根号">
                <span class="sqrt-symbol">√</span>
              </button>
              <button class="math-symbol-btn" @click="insertMathSymbol('²')" title="平方">²</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('³')" title="立方">³</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('∞')" title="无穷">∞</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('∑')" title="求和">∑</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('∫')" title="积分">∫</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('π')" title="圆周率">π</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('α')" title="阿尔法">α</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('β')" title="贝塔">β</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('θ')" title="西塔">θ</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('±')" title="正负">±</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('≤')" title="小于等于">≤</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('≥')" title="大于等于">≥</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('≠')" title="不等于">≠</button>
              <button class="math-symbol-btn" @click="insertMathSymbol('≈')" title="约等于">≈</button>
            </div>
          </div>

          <!-- 文本格式控制 -->
          <div class="property-item">
            <label>文本格式</label>
            <div class="text-format-controls">
              <button @click="applyTextFormat('bold')" :class="{ active: selectedTextHasFormat('bold') }" class="format-btn" title="粗体">
                <strong>B</strong>
              </button>
              <button @click="applyTextFormat('italic')" :class="{ active: selectedTextHasFormat('italic') }" class="format-btn" title="斜体">
                <em>I</em>
              </button>
              <button @click="applyTextFormat('underline')" :class="{ active: selectedTextHasFormat('underline') }" class="format-btn" title="下划线">
                <u>U</u>
              </button>
            </div>
          </div>

          <!-- Font Properties -->
          <div class="property-item">
            <label>Font Family</label>
            <select v-model="firstSelectedElement.fontFamily" @change="updateAllSelectedElements('fontFamily', firstSelectedElement.fontFamily)" class="font-select">
              <option value="Arial, sans-serif">Arial</option>
              <option value="Helvetica, sans-serif">Helvetica</option>
              <option value="Times New Roman, serif">Times New Roman</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="Verdana, sans-serif">Verdana</option>
              <option value="Courier New, monospace">Courier New</option>
              <option value="Impact, sans-serif">Impact</option>
              <option value="Comic Sans MS, cursive">Comic Sans MS</option>
            </select>
          </div>

          <div class="property-item">
            <label>Font Size</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.fontSize" @input="updateAllSelectedElements('fontSize', firstSelectedElement.fontSize)" placeholder="Size" min="8" max="72" />
              <span class="unit-label">px</span>
            </div>
          </div>

          <div class="property-item">
            <label>Font Weight</label>
            <select v-model="firstSelectedElement.fontWeight" @change="updateAllSelectedElements('fontWeight', firstSelectedElement.fontWeight)" class="font-select">
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
              <option value="lighter">Lighter</option>
              <option value="bolder">Bolder</option>
            </select>
          </div>

          <div class="property-item">
            <label>Text Align</label>
            <select v-model="firstSelectedElement.textAlign" @change="updateAllSelectedElements('textAlign', firstSelectedElement.textAlign)" class="font-select">
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
              <option value="justify">Justify</option>
            </select>
          </div>
        </div>

        <!-- Color Controls -->
        <div class="property-section">
          <h4>Colors</h4>

          <!-- Text Color (for text elements) -->
          <div class="property-item" v-if="hasTextElements">
            <label>Text Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedTextColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Text/Font</span>
            </div>
          </div>

          <!-- Fill Color (for shapes and icons) -->
          <div class="property-item" v-if="hasShapesOrIcons">
            <label>Fill Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedFillColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Background/Fill</span>
            </div>
          </div>

          <!-- Border Color (for shapes and images) -->
          <div class="property-item" v-if="hasShapesOrImages">
            <label>Border Color</label>
            <div class="color-input-group">
              <input type="color" v-model="selectedBorderColor" @input="updateSelectedColors" class="color-input-large">
              <span class="color-label">Edge/Border</span>
            </div>
          </div>


        </div>

        <!-- Position & Size - 移动到最下面 -->
        <div class="property-section">
          <h4>位置和尺寸</h4>
          <div class="property-item">
            <label>位置</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.x" @input="updateAllSelectedElements('x', firstSelectedElement.x)" placeholder="X坐标" />
              <input type="number" v-model.number="firstSelectedElement.y" @input="updateAllSelectedElements('y', firstSelectedElement.y)" placeholder="Y坐标" />
            </div>
          </div>

          <div class="property-item">
            <label>尺寸</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.width" @input="updateAllSelectedElements('width', firstSelectedElement.width)" placeholder="宽度" />
              <input type="number" v-model.number="firstSelectedElement.height" @input="updateAllSelectedElements('height', firstSelectedElement.height)" placeholder="高度" />
            </div>
          </div>

          <div class="property-item">
            <label>旋转角度</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.rotation" @input="updateAllSelectedElements('rotation', firstSelectedElement.rotation)" placeholder="角度" min="0" max="359" />
              <button class="btn-small" @click="rotateSelected">🔄 +90°</button>
            </div>
          </div>

          <div class="property-item">
            <label>透明度</label>
            <div class="input-group">
              <input type="range" v-model.number="firstSelectedElement.opacity" @input="updateAllSelectedElements('opacity', firstSelectedElement.opacity)" min="0" max="1" step="0.1" class="opacity-slider" />
              <span class="unit-label">{{ Math.round((firstSelectedElement.opacity || 1) * 100) }}%</span>
            </div>
          </div>

          <!-- Border/Thickness (for shapes and lines) -->
          <div class="property-item" v-if="hasShapesOrLines">
            <label>边框粗细</label>
            <div class="input-group">
              <input type="number" v-model.number="firstSelectedElement.borderWidth" @input="updateAllSelectedElements('borderWidth', firstSelectedElement.borderWidth)" placeholder="宽度" min="1" max="20" />
              <span class="unit-label">px</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Icon Library Modal -->
    <div v-if="iconLibraryVisible" class="modal-overlay" @click="hideIconLibrary">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Icon Library</h3>
          <button class="modal-close" @click="hideIconLibrary">✕</button>
        </div>

        <div class="modal-body">
          <!-- Icon Categories -->
          <div class="icon-categories">
            <button
              v-for="category in iconCategories"
              :key="category.name"
              :class="['category-btn', { active: selectedCategory === category.name }]"
              @click="selectedCategory = category.name"
            >
              {{ category.label }}
            </button>
          </div>

          <!-- Icon Grid -->
          <div class="icon-grid">
            <button
              v-for="icon in currentCategoryIcons"
              :key="icon.name"
              class="icon-item"
              @click="addIconFromLibrary(icon)"
              :title="icon.name"
            >
              {{ icon.symbol }}
            </button>
          </div>

          <!-- Custom SVG Section -->
          <div class="custom-svg-section">
            <h4>Custom SVG</h4>
            <textarea
              v-model="customSvgCode"
              placeholder="Paste your SVG code here..."
              class="svg-textarea"
            ></textarea>
            <button class="btn primary" @click="addCustomSvg" :disabled="!customSvgCode.trim()">
              Add Custom SVG
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Image URL Dialog Modal -->
    <div v-if="imageUrlDialogVisible" class="modal-overlay" @click="hideImageUrlDialog">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>添加图片链接</h3>
          <button class="modal-close" @click="hideImageUrlDialog">✕</button>
        </div>
        <div class="modal-body">
          <div class="url-input-section">
            <label>图片URL:</label>
            <input 
              v-model="imageUrlInput" 
              type="url" 
              placeholder="输入图片链接地址..." 
              class="url-input-full"
              @keyup.enter="addImageFromUrlDialog" />
          </div>
          <div class="modal-actions">
            <button class="btn primary" @click="addImageFromUrlDialog" :disabled="!imageUrlInput.trim()">
              添加图片
            </button>
            <button class="btn" @click="hideImageUrlDialog">
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Background URL Dialog Modal -->
    <div v-if="backgroundUrlDialogVisible" class="modal-overlay" @click="hideBackgroundUrlDialog">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>设置背景图片链接</h3>
          <button class="modal-close" @click="hideBackgroundUrlDialog">✕</button>
        </div>
        <div class="modal-body">
          <div class="url-input-section">
            <label>背景图片URL:</label>
            <input 
              v-model="backgroundUrlInput" 
              type="url" 
              placeholder="输入背景图片链接地址..." 
              class="url-input-full"
              @keyup.enter="setBackgroundFromUrl" />
          </div>
          <div class="modal-actions">
            <button class="btn primary" @click="setBackgroundFromUrl" :disabled="!backgroundUrlInput.trim()">
              设置背景
            </button>
            <button class="btn" @click="hideBackgroundUrlDialog">
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden file inputs -->
    <input ref="imageInput" type="file" @change="handleImageUpload" accept="image/*" style="display: none;" />
    <input ref="backgroundInput" type="file" @change="handleBackgroundUpload" accept="image/*" style="display: none;" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { 
  CopyOutlined, ReloadOutlined, DeleteOutlined, UndoOutlined, RedoOutlined,
  AlignLeftOutlined, AlignCenterOutlined, AlignRightOutlined, ZoomInOutlined,
  ZoomOutOutlined, BorderOutlined, FileImageOutlined, SaveOutlined,
  PictureOutlined, LinkOutlined, EditOutlined, SnippetsOutlined,
  FontSizeOutlined, FileTextOutlined, TagOutlined, FileAddOutlined,
  ImportOutlined, ExportOutlined, DownOutlined, UploadOutlined, FolderAddOutlined,
  RotateRightOutlined, FunctionOutlined, AppstoreOutlined, StarOutlined
} from '@ant-design/icons-vue'

// State
const elements = ref([])
const selectedElements = ref([])
const history = ref([])
const historyIndex = ref(-1)
const isDragging = ref(false)
const isResizing = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const mousePosition = ref({ x: 0, y: 0 })
const canvasBackground = ref('#ffffff')
const canvas = ref(null)
const fileInput = ref(null)
const imageInput = ref(null)
const imageUrl = ref('')
const draggedElement = ref(null)
const resizeData = ref({ elementId: null, handle: null, startX: 0, startY: 0, startWidth: 0, startHeight: 0 })
const contextMenu = ref({ visible: false, x: 0, y: 0, elementId: null })
const clipboard = ref(null)

// Rotation state
const isRotating = ref(false)
const rotateData = ref({ elementId: null, startAngle: 0, centerX: 0, centerY: 0 })

// Selection area for drag selection
const selectionArea = ref({ active: false, startX: 0, startY: 0, endX: 0, endY: 0 })
const isSelecting = ref(false)

// Color controls
const selectedFillColor = ref('#ffffff')
const selectedBorderColor = ref('#000000')
const selectedTextColor = ref('#000000')
const selectedShadowColor = ref('#000000')

// View controls
const zoomLevel = ref(1)
const showGrid = ref(false)
const canvasOffset = ref({ x: 0, y: 0 })

// Icon library
const iconLibraryVisible = ref(false)
const selectedCategory = ref('common')
const customSvgCode = ref('')

// Image URL dialog
const imageUrlDialogVisible = ref(false)
const imageUrlInput = ref('')

// Background dialog
const backgroundUrlDialogVisible = ref(false)
const backgroundUrlInput = ref('')
const backgroundInput = ref(null)
const canvasBackgroundImage = ref('')

// Text formatting
const textFormatActive = ref({
  bold: false,
  italic: false,
  underline: false
})

// Drawing mode
const isDrawingMode = ref(false)
const isDrawing = ref(false)
const currentPath = ref('')
const drawingStartX = ref(0)
const drawingStartY = ref(0)

// Computed
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// Color controls computed
const hasTextElements = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'text'
  })
})

const hasShapesOrIcons = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'icon')
  })
})

const hasShapesOrImages = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'image')
  })
})

const hasShapesOrLines = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'shape'
  })
})

const firstSelectedElement = computed(() => {
  if (selectedElements.value.length > 0) {
    return getElementById(selectedElements.value[0]) || {}
  }
  return {}
})

const hasShapesOrImagesOrText = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && (element.type === 'shape' || element.type === 'image' || element.type === 'text')
  })
})

const hasGroupSelected = computed(() => {
  return selectedElements.value.some(id => {
    const element = getElementById(id)
    return element && element.type === 'group'
  })
})

const layeredElements = computed(() => {
  return [...elements.value].reverse() // Show top elements first in layers panel
})

// 透明度计算属性
const opacityValue = computed({
  get: () => Math.round((firstSelectedElement.value.opacity || 1) * 100),
  set: (value) => {
    const opacity = value / 100
    updateAllSelectedElements('opacity', opacity)
  }
})

// 更新透明度方法
const updateOpacity = (value) => {
  const opacity = value / 100
  updateAllSelectedElements('opacity', opacity)
}

// Icon library data
const iconCategories = ref([
  { name: 'common', label: 'Common' },
  { name: 'arrows', label: 'Arrows' },
  { name: 'symbols', label: 'Symbols' },
  { name: 'objects', label: 'Objects' },
  { name: 'nature', label: 'Nature' },
  { name: 'people', label: 'People' }
])

const iconLibrary = ref({
  common: [
    { name: 'star', symbol: '⭐' },
    { name: 'heart', symbol: '❤️' },
    { name: 'check', symbol: '✅' },
    { name: 'cross', symbol: '❌' },
    { name: 'warning', symbol: '⚠️' },
    { name: 'info', symbol: 'ℹ️' },
    { name: 'home', symbol: '🏠' },
    { name: 'user', symbol: '👤' },
    { name: 'mail', symbol: '📧' },
    { name: 'phone', symbol: '📞' },
    { name: 'location', symbol: '📍' },
    { name: 'calendar', symbol: '📅' },
    { name: 'clock', symbol: '🕐' },
    { name: 'settings', symbol: '⚙️' },
    { name: 'search', symbol: '🔍' },
    { name: 'download', symbol: '⬇️' },
    { name: 'upload', symbol: '⬆️' },
    { name: 'save', symbol: '💾' },
    { name: 'edit', symbol: '✏️' },
    { name: 'delete', symbol: '🗑️' }
  ],
  arrows: [
    { name: 'arrow-up', symbol: '⬆️' },
    { name: 'arrow-down', symbol: '⬇️' },
    { name: 'arrow-left', symbol: '⬅️' },
    { name: 'arrow-right', symbol: '➡️' },
    { name: 'arrow-up-right', symbol: '↗️' },
    { name: 'arrow-down-right', symbol: '↘️' },
    { name: 'arrow-down-left', symbol: '↙️' },
    { name: 'arrow-up-left', symbol: '↖️' },
    { name: 'refresh', symbol: '🔄' },
    { name: 'undo', symbol: '↩️' },
    { name: 'redo', symbol: '↪️' },
    { name: 'forward', symbol: '⏩' },
    { name: 'backward', symbol: '⏪' },
    { name: 'play', symbol: '▶️' },
    { name: 'pause', symbol: '⏸️' },
    { name: 'stop', symbol: '⏹️' }
  ],
  symbols: [
    { name: 'plus', symbol: '➕' },
    { name: 'minus', symbol: '➖' },
    { name: 'multiply', symbol: '✖️' },
    { name: 'divide', symbol: '➗' },
    { name: 'equals', symbol: '🟰' },
    { name: 'question', symbol: '❓' },
    { name: 'exclamation', symbol: '❗' },
    { name: 'copyright', symbol: '©️' },
    { name: 'trademark', symbol: '™️' },
    { name: 'registered', symbol: '®️' },
    { name: 'dollar', symbol: '💲' },
    { name: 'euro', symbol: '💶' },
    { name: 'pound', symbol: '💷' },
    { name: 'yen', symbol: '💴' },
    { name: 'percent', symbol: '💯' },
    { name: 'hash', symbol: '#️⃣' }
  ],
  objects: [
    { name: 'computer', symbol: '💻' },
    { name: 'mobile', symbol: '📱' },
    { name: 'tablet', symbol: '📱' },
    { name: 'camera', symbol: '📷' },
    { name: 'printer', symbol: '🖨️' },
    { name: 'keyboard', symbol: '⌨️' },
    { name: 'mouse', symbol: '🖱️' },
    { name: 'monitor', symbol: '🖥️' },
    { name: 'speaker', symbol: '🔊' },
    { name: 'microphone', symbol: '🎤' },
    { name: 'headphones', symbol: '🎧' },
    { name: 'book', symbol: '📚' },
    { name: 'folder', symbol: '📁' },
    { name: 'file', symbol: '📄' },
    { name: 'image', symbol: '🖼️' },
    { name: 'video', symbol: '🎥' }
  ],
  nature: [
    { name: 'sun', symbol: '☀️' },
    { name: 'moon', symbol: '🌙' },
    { name: 'star-nature', symbol: '⭐' },
    { name: 'cloud', symbol: '☁️' },
    { name: 'rain', symbol: '🌧️' },
    { name: 'snow', symbol: '❄️' },
    { name: 'lightning', symbol: '⚡' },
    { name: 'fire', symbol: '🔥' },
    { name: 'water', symbol: '💧' },
    { name: 'earth', symbol: '🌍' },
    { name: 'tree', symbol: '🌳' },
    { name: 'flower', symbol: '🌸' },
    { name: 'leaf', symbol: '🍃' },
    { name: 'mountain', symbol: '⛰️' },
    { name: 'ocean', symbol: '🌊' },
    { name: 'rainbow', symbol: '🌈' }
  ],
  people: [
    { name: 'person', symbol: '👤' },
    { name: 'people', symbol: '👥' },
    { name: 'family', symbol: '👨‍👩‍👧‍👦' },
    { name: 'man', symbol: '👨' },
    { name: 'woman', symbol: '👩' },
    { name: 'child', symbol: '🧒' },
    { name: 'baby', symbol: '👶' },
    { name: 'elder', symbol: '👴' },
    { name: 'thumbs-up', symbol: '👍' },
    { name: 'thumbs-down', symbol: '👎' },
    { name: 'clap', symbol: '👏' },
    { name: 'wave', symbol: '👋' },
    { name: 'peace', symbol: '✌️' },
    { name: 'ok-hand', symbol: '👌' },
    { name: 'pointing', symbol: '👉' },
    { name: 'muscle', symbol: '💪' }
  ]
})

const currentCategoryIcons = computed(() => {
  return iconLibrary.value[selectedCategory.value] || []
})

// Helper functions
const generateId = () => Math.random().toString(36).substr(2, 9)

const getElementById = (id) => elements.value.find(el => el.id === id)

// Alignment functions
const alignLeft = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const leftmost = Math.min(...selectedElements.value.map(id => getElementById(id).x))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = leftmost
  })
}

const alignRight = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const rightmost = Math.max(...selectedElements.value.map(id => {
    const el = getElementById(id)
    return el.x + el.width
  }))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = rightmost - element.width
  })
}

const alignCenter = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const centerX = selectedElements.value.reduce((sum, id) => {
    const el = getElementById(id)
    return sum + el.x + el.width / 2
  }, 0) / selectedElements.value.length
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.x = centerX - element.width / 2
  })
}

const alignTop = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const topmost = Math.min(...selectedElements.value.map(id => getElementById(id).y))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = topmost
  })
}

const alignBottom = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const bottommost = Math.max(...selectedElements.value.map(id => {
    const el = getElementById(id)
    return el.y + el.height
  }))
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = bottommost - element.height
  })
}

const alignMiddle = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  const centerY = selectedElements.value.reduce((sum, id) => {
    const el = getElementById(id)
    return sum + el.y + el.height / 2
  }, 0) / selectedElements.value.length
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.y = centerY - element.height / 2
  })
}

// Layer management functions
const bringToFront = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const index = elements.value.findIndex(el => el.id === id)
      elements.value.splice(index, 1)
      elements.value.push(element)
    }
  })
}

const sendToBack = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const index = elements.value.findIndex(el => el.id === id)
      elements.value.splice(index, 1)
      elements.value.unshift(element)
    }
  })
}

// Zoom functions
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.25, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.25, 0.25)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const setZoomLevel = (value) => {
  zoomLevel.value = value
}

const getCanvasBackgroundImage = () => {
  const images = []
  
  if (canvasBackgroundImage.value) {
    images.push(`url(${canvasBackgroundImage.value})`)
  }
  
  if (showGrid.value) {
    images.push('linear-gradient(#ccc 1px, transparent 1px)', 'linear-gradient(90deg, #ccc 1px, transparent 1px)')
  }
  
  return images.length > 0 ? images.join(', ') : 'none'
}

const toggleGrid = () => {
  showGrid.value = !showGrid.value
}

// Layer panel functions
const selectLayer = (id, event) => {
  selectElement(id, event)
}

const getLayerIcon = (element) => {
  switch (element.type) {
    case 'text': return '📝'
    case 'shape': return '🔷'
    case 'icon': return '🎭'
    case 'image': return '🖼️'
    case 'group': return '📦'
    default: return '📄'
  }
}

const toggleVisibility = (id) => {
  const element = getElementById(id)
  if (element) {
    element.hidden = !element.hidden
  }
}

const toggleLock = (id) => {
  const element = getElementById(id)
  if (element) {
    element.locked = !element.locked
  }
}

const lockSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.locked = true
  })
}

const unlockSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.locked = false
  })
}

const hideSelected = () => {
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) element.hidden = true
  })
}

const renameLayer = (id) => {
  const element = getElementById(id)
  if (element) {
    element.isRenaming = true
  }
}

const finishRename = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.name = event.target.textContent
    element.isRenaming = false
  }
}

// Group functions
const groupElements = () => {
  if (selectedElements.value.length < 2) return
  saveState()
  
  const groupedElements = selectedElements.value.map(id => getElementById(id))
  const bounds = getBounds(groupedElements)
  
  const group = {
    id: generateId(),
    type: 'group',
    x: bounds.left,
    y: bounds.top,
    width: bounds.width,
    height: bounds.height,
    children: [...selectedElements.value],
    rotation: 0,
    opacity: 1
  }
  
  // Remove individual elements from main array
  elements.value = elements.value.filter(el => !selectedElements.value.includes(el.id))
  
  // Add group
  elements.value.push(group)
  selectedElements.value = [group.id]
}

const ungroupElements = () => {
  const groupIds = selectedElements.value.filter(id => {
    const element = getElementById(id)
    return element && element.type === 'group'
  })
  
  if (groupIds.length === 0) return
  saveState()
  
  groupIds.forEach(groupId => {
    const group = getElementById(groupId)
    if (group && group.children) {
      // Add children back to main array
      group.children.forEach(childId => {
        const child = getElementById(childId)
        if (child) {
          elements.value.push(child)
        }
      })
      
      // Remove group
      elements.value = elements.value.filter(el => el.id !== groupId)
    }
  })
  
  selectedElements.value = []
}

const getBounds = (elements) => {
  const left = Math.min(...elements.map(el => el.x))
  const top = Math.min(...elements.map(el => el.y))
  const right = Math.max(...elements.map(el => el.x + el.width))
  const bottom = Math.max(...elements.map(el => el.y + el.height))
  
  return {
    left,
    top,
    right,
    bottom,
    width: right - left,
    height: bottom - top
  }
}

// Export functions
const exportAsPNG = () => {
  const canvasEl = canvas.value
  const rect = canvasEl.getBoundingClientRect()
  
  // Create a temporary canvas
  const tempCanvas = document.createElement('canvas')
  const ctx = tempCanvas.getContext('2d')
  
  // Set canvas size
  tempCanvas.width = rect.width
  tempCanvas.height = rect.height
  
  // Fill background
  ctx.fillStyle = canvasBackground.value
  ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height)
  
  // This is a simplified export - in a real app, you'd need to render each element
  alert('PNG export functionality requires additional canvas rendering implementation')
}

const exportAsSVG = () => {
  let svg = `<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">`
  svg += `<rect width="100%" height="100%" fill="${canvasBackground.value}"/>`
  
  elements.value.forEach(element => {
    if (element.type === 'shape' && element.subtype === 'rectangle') {
      svg += `<rect x="${element.x}" y="${element.y}" width="${element.width}" height="${element.height}" fill="${element.color}" stroke="${element.borderColor}" stroke-width="${element.borderWidth}"/>`
    } else if (element.type === 'text') {
      svg += `<text x="${element.x}" y="${element.y + element.fontSize}" font-family="${element.fontFamily}" font-size="${element.fontSize}" fill="${element.color}">${element.content}</text>`
    }
  })
  
  svg += '</svg>'
  
  const blob = new Blob([svg], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `visual-editor-export-${Date.now()}.svg`
  a.click()
  URL.revokeObjectURL(url)
}

// Shadow effect functions
const updateSelectedShadow = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element.shadowColor = selectedShadowColor.value
    }
  })
}

const saveState = () => {
  const newState = JSON.parse(JSON.stringify(elements.value))
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(newState)
  historyIndex.value++
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

// Drag and Drop from Sidebar and Ribbon
const startDragFromSidebar = (elementType, event) => {
  event.dataTransfer.setData('text/plain', elementType)
  event.dataTransfer.effectAllowed = 'copy'
}

const startDragFromRibbon = (elementType, event) => {
  event.dataTransfer.setData('text/plain', elementType)
  event.dataTransfer.effectAllowed = 'copy'
}

const handleCanvasDrop = (event) => {
  event.preventDefault()
  const elementType = event.dataTransfer.getData('text/plain')

  if (!elementType) return

  const rect = canvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // Handle text elements
  if (elementType.startsWith('text-')) {
    const textType = elementType.replace('text-', '')
    addTextElementAtPosition(textType, x, y)
  } else if (elementType.startsWith('icon-')) {
    const iconType = elementType.replace('icon-', '')
    addIconAtPosition(iconType, x, y)
  } else {
    // Handle shape elements
    addShapeAtPosition(elementType, x, y)
  }
}

const addTextElementAtPosition = (type, x, y) => {
  saveState()
  const element = {
    id: generateId(),
    type: 'text',
    subtype: type,
    content: type === 'heading' ? 'Heading text' : type === 'paragraph' ? 'Paragraph text' : 'Label text',
    x: x - 50, // Center the element
    y: y - 15,
    width: 100,
    height: 30,
    color: '#000000',
    fontSize: type === 'heading' ? 24 : type === 'paragraph' ? 16 : 14,
    fontFamily: 'Arial, sans-serif',
    fontWeight: type === 'heading' ? 'bold' : 'normal',
    textAlign: 'left',
    rotation: 0
  }
  elements.value.push(element)
  selectedElements.value = [element.id]
}

const addIconAtPosition = (iconType, x, y) => {
  saveState()
  const element = {
    id: generateId(),
    type: 'icon',
    subtype: iconType,
    x: x - 40, // Center the element
    y: y - 40,
    width: 80,
    height: 80,
    content: getIconSymbol(iconType),
    color: '#000000',
    rotation: 0
  }
  elements.value.push(element)
  selectedElements.value = [element.id]
}

const getIconSymbol = (iconType) => {
  const iconMap = {
    star: '⭐',
    heart: '❤️',
    check: '✅',
    warning: '⚠️',
    home: '🏠',
    user: '👤',
    mail: '📧',
    phone: '📞',
    location: '📍',
    calendar: '📅',
    clock: '🕐',
    settings: '⚙️',
    search: '🔍',
    download: '⬇️',
    upload: '⬆️'
  }
  return iconMap[iconType] || '⭐'
}

const addShapeAtPosition = (shapeType, x, y) => {
  saveState()

  // Shape-specific dimensions and properties
  const shapeDefaults = {
    rectangle: { width: 120, height: 80 },
    square: { width: 80, height: 80 },
    circle: { width: 80, height: 80 },
    oval: { width: 120, height: 80 },
    triangle: { width: 80, height: 80 },
    diamond: { width: 80, height: 80 },
    line: { width: 100, height: 2 },
    arrow: { width: 100, height: 20 },
    curve: { width: 100, height: 50 },
    polygon: { width: 80, height: 80 },
    hexagon: { width: 80, height: 80 },
    octagon: { width: 80, height: 80 },
    'dotted-line': { width: 100, height: 10 },
    'dotted-curve': { width: 100, height: 50 },
    'freehand-circle': { width: 80, height: 80 },
    'freehand-rect': { width: 100, height: 60 },
    'freehand-line': { width: 100, height: 2 },
    'freehand-curve': { width: 100, height: 50 },
    'freehand-arrow': { width: 100, height: 20 },
    'freehand-star': { width: 80, height: 80 }
  }

  const defaults = shapeDefaults[shapeType] || { width: 80, height: 80 }

  // Special colors for certain shapes
  let fillColor = '#ffffff'
  let borderColor = '#000000'

  if (['star', 'heart'].includes(shapeType)) {
    fillColor = '#ff6b6b'
    borderColor = '#d63031'
  } else if (['triangle', 'diamond', 'hexagon', 'octagon', 'polygon'].includes(shapeType)) {
    fillColor = '#ffffff'
    borderColor = '#000000'
  }

  const element = {
    id: generateId(),
    type: 'shape',
    subtype: shapeType,
    x: x - defaults.width / 2, // Center the element
    y: y - defaults.height / 2,
    width: defaults.width,
    height: defaults.height,
    color: fillColor,
    borderColor: borderColor,
    borderWidth: 2,
    rotation: 0
  }

  elements.value.push(element)
  selectedElements.value = [element.id]
}

// Element creation
const addTextElement = (type) => {
  saveState()
  const element = {
    id: generateId(),
    type: 'text',
    subtype: type,
    x: 100,
    y: 100,
    width: 200,
    height: type === 'heading' ? 40 : 30,
    content: type === 'heading' ? 'Heading' : type === 'paragraph' ? 'Paragraph text' : 'Label',
    color: '#000000',
    fontSize: type === 'heading' ? 24 : 16,
    fontWeight: type === 'heading' ? 'bold' : 'normal',
    rotation: 0
  }
  elements.value.push(element)
}

const addShape = (shapeType) => {
  saveState()

  // Shape-specific dimensions and properties
  const shapeDefaults = {
    rectangle: { width: 120, height: 80 },
    square: { width: 100, height: 100 },
    circle: { width: 100, height: 100 },
    oval: { width: 120, height: 80 },
    triangle: { width: 100, height: 100 },
    diamond: { width: 100, height: 100 },
    line: { width: 150, height: 2 },
    arrow: { width: 150, height: 20 },
    curve: { width: 120, height: 60 },
    polygon: { width: 100, height: 100 },
    star: { width: 100, height: 100 },
    heart: { width: 100, height: 100 },
    hexagon: { width: 100, height: 100 },
    octagon: { width: 100, height: 100 },
    parallelogram: { width: 120, height: 80 },
    trapezoid: { width: 120, height: 80 },
    cross: { width: 80, height: 80 },
    'rounded-rect': { width: 120, height: 80 },
    'dotted-line': { width: 150, height: 2 },
    'dotted-curve': { width: 120, height: 60 },
    'freehand-circle': { width: 100, height: 100 },
    'freehand-rect': { width: 120, height: 80 },
    'freehand-line': { width: 150, height: 2 },
    'freehand-curve': { width: 120, height: 60 },
    'freehand-arrow': { width: 150, height: 20 },
    'freehand-star': { width: 100, height: 100 }
  }

  const defaults = shapeDefaults[shapeType] || { width: 100, height: 100 }

  // Set different colors for different shape types to make them more visible
  let fillColor = '#ffffff'  // Default white
  let borderColor = '#000000' // Default black

  // Special colors for certain shapes to make them more visible
  if (['star', 'heart'].includes(shapeType)) {
    fillColor = '#ff6b6b'  // Red for star and heart
    borderColor = '#d63031'
  } else if (['triangle', 'diamond', 'hexagon', 'octagon', 'polygon'].includes(shapeType)) {
    fillColor = '#ffffff'  // White for line-based shapes
    borderColor = '#000000' // Black border
  } else if (['cross'].includes(shapeType)) {
    fillColor = '#fd79a8'  // Pink for cross
    borderColor = '#e84393'
  }

  const element = {
    id: generateId(),
    type: 'shape',
    subtype: shapeType,
    x: 150,
    y: 150,
    width: defaults.width,
    height: defaults.height,
    color: fillColor,
    borderColor: borderColor,
    borderWidth: 2,
    rotation: 0
  }
  elements.value.push(element)
}

const addIcon = (iconType) => {
  saveState()
  const icons = {
    star: '⭐',
    heart: '❤️',
    check: '✅',
    cross: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    home: '🏠',
    user: '👤',
    mail: '📧',
    phone: '📞',
    location: '📍',
    calendar: '📅',
    clock: '🕐',
    settings: '⚙️',
    search: '🔍',
    download: '⬇️',
    upload: '⬆️',
    save: '💾',
    edit: '✏️',
    delete: '🗑️',
    'arrow-up': '⬆️',
    'arrow-down': '⬇️',
    'arrow-left': '⬅️',
    'arrow-right': '➡️',
    'arrow-up-right': '↗️',
    'arrow-down-right': '↘️',
    'arrow-down-left': '↙️',
    'arrow-up-left': '↖️',
    refresh: '🔄',
    undo: '↩️',
    redo: '↪️',
    forward: '⏩',
    backward: '⏪',
    play: '▶️',
    pause: '⏸️',
    stop: '⏹️',
    plus: '➕',
    minus: '➖',
    multiply: '✖️',
    divide: '➗',
    equals: '🟰',
    question: '❓',
    exclamation: '❗',
    copyright: '©️',
    trademark: '™️',
    registered: '®️',
    dollar: '💲',
    euro: '💶',
    pound: '💷',
    yen: '💴',
    percent: '💯',
    hash: '#️⃣',
    computer: '💻',
    mobile: '📱',
    tablet: '📱',
    camera: '📷',
    printer: '🖨️',
    keyboard: '⌨️',
    mouse: '🖱️',
    monitor: '🖥️',
    speaker: '🔊',
    microphone: '🎤',
    headphones: '🎧',
    book: '📚',
    folder: '📁',
    file: '📄',
    image: '🖼️',
    video: '🎥',
    sun: '☀️',
    moon: '🌙',
    'star-nature': '⭐',
    cloud: '☁️',
    rain: '🌧️',
    snow: '❄️',
    lightning: '⚡',
    fire: '🔥',
    water: '💧',
    earth: '🌍',
    tree: '🌳',
    flower: '🌸',
    leaf: '🍃',
    mountain: '⛰️',
    ocean: '🌊',
    rainbow: '🌈',
    person: '👤',
    people: '👥',
    family: '👨‍👩‍👧‍👦',
    man: '👨',
    woman: '👩',
    child: '🧒',
    baby: '👶',
    elder: '👴',
    'thumbs-up': '👍',
    'thumbs-down': '👎',
    clap: '👏',
    wave: '👋',
    peace: '✌️',
    'ok-hand': '👌',
    pointing: '👉',
    muscle: '💪'
  }
  
  const element = {
    id: generateId(),
    type: 'icon',
    subtype: iconType,
    x: 200,
    y: 200,
    width: 40,
    height: 40,
    content: icons[iconType] || '⭐',
    color: '#000000',
    rotation: 0
  }
  elements.value.push(element)
}

// Icon library functions
const showIconLibrary = () => {
  iconLibraryVisible.value = true
}

const hideIconLibrary = () => {
  iconLibraryVisible.value = false
  customSvgCode.value = ''
}

const addIconFromLibrary = (icon) => {
  addIcon(icon.name)
  hideIconLibrary()
}

const addCustomSvg = () => {
  if (!customSvgCode.value.trim()) return

  saveState()
  const element = {
    id: generateId(),
    type: 'custom-svg',
    x: 150,
    y: 150,
    width: 100,
    height: 100,
    svgCode: customSvgCode.value.trim(),
    rotation: 0
  }
  elements.value.push(element)
  hideIconLibrary()
}

// Media upload functions
const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event) => {
  const file = event.target.files[0]
  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      addImageElement(e.target.result, file.name)
    }
    reader.readAsDataURL(file)
  }
  // Reset input
  event.target.value = ''
}

const addImageFromUrl = () => {
  if (imageUrl.value.trim()) {
    addImageElement(imageUrl.value, 'Image from URL')
    imageUrl.value = ''
  }
}

// Image URL dialog functions
const showImageUrlDialog = () => {
  imageUrlDialogVisible.value = true
  imageUrlInput.value = ''
}

const hideImageUrlDialog = () => {
  imageUrlDialogVisible.value = false
  imageUrlInput.value = ''
}

// Background functions
const updateCanvasBackground = () => {
  canvasBackgroundImage.value = ''
}

const selectBackgroundImage = () => {
  backgroundInput.value?.click()
}

const handleBackgroundUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      canvasBackgroundImage.value = e.target.result
      canvasBackground.value = '#ffffff'
    }
    reader.readAsDataURL(file)
  }
}

const showBackgroundUrlDialog = () => {
  backgroundUrlDialogVisible.value = true
}

const hideBackgroundUrlDialog = () => {
  backgroundUrlDialogVisible.value = false
  backgroundUrlInput.value = ''
}

const setBackgroundFromUrl = () => {
  if (backgroundUrlInput.value.trim()) {
    canvasBackgroundImage.value = backgroundUrlInput.value.trim()
    canvasBackground.value = '#ffffff'
    hideBackgroundUrlDialog()
  }
}

const clearBackground = () => {
  canvasBackground.value = '#ffffff'
  canvasBackgroundImage.value = ''
}

// Math symbols function
const addMathSymbol = (symbol) => {
  const id = generateId()
  const element = {
    id,
    type: 'text',
    content: symbol,
    x: 100,
    y: 100,
    width: 50,
    height: 30,
    fontSize: 20,
    fontFamily: 'Arial, sans-serif',
    color: '#000000',
    fontWeight: 'normal',
    textAlign: 'center',
    rotation: 0,
    opacity: 1
  }
  
  elements.value.push(element)
  selectedElements.value = [id]
  saveState()
}

// Math shape function
const addMathShape = (shapeType) => {
  saveState()
  
  const mathShapeDefaults = {
    'plus': { width: 60, height: 60, symbol: '+' },
    'minus': { width: 60, height: 20, symbol: '−' },
    'multiply': { width: 60, height: 60, symbol: '×' },
    'divide': { width: 60, height: 60, symbol: '÷' },
    'equals': { width: 80, height: 40, symbol: '=' },
    'less-than': { width: 60, height: 60, symbol: '<' },
    'greater-than': { width: 60, height: 60, symbol: '>' },
    'less-equal': { width: 60, height: 60, symbol: '≤' },
    'greater-equal': { width: 60, height: 60, symbol: '≥' },
    'not-equal': { width: 60, height: 60, symbol: '≠' },
    'sqrt': { width: 80, height: 60, symbol: '√' },
    'pi': { width: 60, height: 60, symbol: 'π' },
    'infinity': { width: 80, height: 60, symbol: '∞' },
    'plus-minus': { width: 60, height: 60, symbol: '±' },
    'division-bracket': { width: 120, height: 80, symbol: '', isDivisionBracket: true },
  }
  
  const defaults = mathShapeDefaults[shapeType] || { width: 60, height: 60, symbol: '+' }
  
  const element = {
    id: generateId(),
    type: 'math-shape',
    subtype: shapeType,
    x: 150,
    y: 150,
    width: defaults.width,
    height: defaults.height,
    symbol: defaults.symbol,
    color: '#000000',
    fontSize: 24,
    fontWeight: 'bold',
    rotation: 0,
    isDivisionBracket: defaults.isDivisionBracket || false
  }
  
  elements.value.push(element)
  selectedElements.value = [element.id]
}

// Text formatting functions
const toggleTextFormat = (format) => {
  if (selectedElements.value.length === 0) return
  
  textFormatActive.value[format] = !textFormatActive.value[format]
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element && (element.type === 'text' || element.type === 'label' || element.type === 'title')) {
      switch (format) {
        case 'bold':
          element.fontWeight = textFormatActive.value.bold ? 'bold' : 'normal'
          break
        case 'italic':
          element.fontStyle = textFormatActive.value.italic ? 'italic' : 'normal'
          break
        case 'underline':
          element.textDecoration = textFormatActive.value.underline ? 'underline' : 'none'
          break
      }
    }
  })
  
  saveState()
}

// Insert math symbol into selected text elements
const insertMathSymbol = (symbol) => {
  if (selectedElements.value.length === 0) return
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element && (element.type === 'text' || element.type === 'label' || element.type === 'title')) {
      element.content = element.content + symbol
    }
  })
  
  saveState()
}

// Apply text format to selected elements
const applyTextFormat = (format) => {
  if (selectedElements.value.length === 0) return
  
  const hasFormat = selectedTextHasFormat(format)
  
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element && (element.type === 'text' || element.type === 'label' || element.type === 'title')) {
      switch (format) {
        case 'bold':
          element.fontWeight = hasFormat ? 'normal' : 'bold'
          break
        case 'italic':
          element.fontStyle = hasFormat ? 'normal' : 'italic'
          break
        case 'underline':
          element.textDecoration = hasFormat ? 'none' : 'underline'
          break
      }
    }
  })
  
  saveState()
}

// Check if selected text has specific format
const selectedTextHasFormat = (format) => {
  if (selectedElements.value.length === 0) return false
  
  const textElements = selectedElements.value.map(id => getElementById(id))
    .filter(el => el && (el.type === 'text' || el.type === 'label' || el.type === 'title'))
  
  if (textElements.length === 0) return false
  
  return textElements.some(element => {
    switch (format) {
      case 'bold':
        return element.fontWeight === 'bold'
      case 'italic':
        return element.fontStyle === 'italic'
      case 'underline':
        return element.textDecoration === 'underline'
      default:
        return false
    }
  })
}

const addImageFromUrlDialog = () => {
  if (imageUrlInput.value.trim()) {
    addImageElement(imageUrlInput.value.trim(), 'Image from URL')
    hideImageUrlDialog()
  }
}

// Drawing mode functions
const enableDrawingMode = () => {
  isDrawingMode.value = !isDrawingMode.value
  if (!isDrawingMode.value) {
    isDrawing.value = false
    currentPath.value = ''
  }
}

const startDrawing = (event) => {
  if (!isDrawingMode.value) return
  
  isDrawing.value = true
  const rect = canvas.value.getBoundingClientRect()
  drawingStartX.value = event.clientX - rect.left
  drawingStartY.value = event.clientY - rect.top
  currentPath.value = `M ${drawingStartX.value} ${drawingStartY.value}`
}

const continueDrawing = (event) => {
  if (!isDrawingMode.value || !isDrawing.value) return
  
  const rect = canvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  currentPath.value += ` L ${x} ${y}`
}

const finishDrawing = () => {
  if (!isDrawingMode.value || !isDrawing.value || !currentPath.value) return
  
  saveState()
  
  // Create a freehand drawing element
  const element = {
    id: generateId(),
    type: 'freehand-drawing',
    x: drawingStartX.value - 50,
    y: drawingStartY.value - 50,
    width: 100,
    height: 100,
    path: currentPath.value,
    strokeColor: '#000000',
    strokeWidth: 2,
    rotation: 0
  }
  
  elements.value.push(element)
  selectedElements.value = [element.id]
  
  // Reset drawing state
  isDrawing.value = false
  currentPath.value = ''
}

const addImageElement = (src, name) => {
  saveState()

  // Create a temporary image to get dimensions
  const img = new Image()
  img.onload = () => {
    const maxWidth = 300
    const maxHeight = 300
    let width = img.width
    let height = img.height

    // Scale down if too large
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height)
      width = width * ratio
      height = height * ratio
    }

    const element = {
      id: generateId(),
      type: 'image',
      x: 100,
      y: 100,
      width: Math.round(width),
      height: Math.round(height),
      src: src,
      name: name,
      borderColor: '#000000',
      borderWidth: 0,
      rotation: 0
    }
    elements.value.push(element)
  }

  img.onerror = () => {
    alert('Failed to load image. Please check the URL or file.')
  }

  img.src = src
}

// Selection
const selectElement = (id, event) => {
  if (event.ctrlKey || event.metaKey) {
    if (selectedElements.value.includes(id)) {
      selectedElements.value = selectedElements.value.filter(el => el !== id)
    } else {
      selectedElements.value.push(id)
    }
  } else {
    selectedElements.value = [id]
  }

  // Update color controls based on selected elements
  updateColorControls()
}

const updateColorControls = () => {
  if (selectedElements.value.length > 0) {
    const firstElement = getElementById(selectedElements.value[0])
    if (firstElement) {
      selectedFillColor.value = firstElement.color || '#ffffff'
      selectedBorderColor.value = firstElement.borderColor || '#000000'
      selectedTextColor.value = firstElement.color || '#000000'
    }
  }
}

const updateAllSelectedElements = (property, value) => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element[property] = value
    }
  })
}

const handleCanvasClick = (event) => {
  // Only clear selection if clicking directly on canvas (not on elements)
  if (event.target === canvas.value && !isSelecting.value) {
    selectedElements.value = []
  }
  hideContextMenu()
  // Ensure canvas is focused for keyboard shortcuts
  canvas.value?.focus()
}

const handleCanvasDoubleClick = (event) => {
  if (event.target === canvas.value) {
    // Create a text label at the double-click position
    const rect = canvas.value.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    saveState()
    const element = {
      id: generateId(),
      type: 'text',
      subtype: 'label',
      x: x - 50, // Center the text
      y: y - 15,
      width: 100,
      height: 30,
      content: 'Label',
      color: '#000000',
      fontSize: 16,
      fontWeight: 'normal',
      fontFamily: 'Arial, sans-serif',
      textAlign: 'left',
      rotation: 0
    }
    elements.value.push(element)

    // Select the new element and start editing
    selectedElements.value = [element.id]

    // Start editing the text immediately
    setTimeout(() => {
      startEditing(element.id)
    }, 100)
  }
}

const handleCanvasMouseDown = (event) => {
  // Handle drawing mode
  if (isDrawingMode.value && event.target === canvas.value) {
    startDrawing(event)
    return
  }
  
  // Only start selection if clicking directly on canvas (not on elements) and not dragging
  if (event.target === canvas.value && !isDragging.value) {
    const rect = canvas.value.getBoundingClientRect()
    
    // Clear selection if not holding Ctrl/Cmd
    if (!event.ctrlKey && !event.metaKey) {
      selectedElements.value = []
    }
    
    // Start drag selection
    isSelecting.value = true
    selectionArea.value = {
      active: true,
      startX: event.clientX - rect.left,
      startY: event.clientY - rect.top,
      endX: event.clientX - rect.left,
      endY: event.clientY - rect.top
    }
    hideContextMenu()
    console.log('Started selection at:', selectionArea.value.startX, selectionArea.value.startY)
  }
}

const handleCanvasMouseMove = (event) => {
  continueDrawing(event)
}

const handleCanvasMouseUp = (event) => {
  finishDrawing()
}

const completeSelection = () => {
  if (!selectionArea.value.active) return

  const minX = Math.min(selectionArea.value.startX, selectionArea.value.endX)
  const maxX = Math.max(selectionArea.value.startX, selectionArea.value.endX)
  const minY = Math.min(selectionArea.value.startY, selectionArea.value.endY)
  const maxY = Math.max(selectionArea.value.startY, selectionArea.value.endY)

  // Only select if the selection area is large enough (avoid accidental selections)
  if (Math.abs(maxX - minX) < 10 || Math.abs(maxY - minY) < 10) {
    selectedElements.value = []
    return
  }

  const selectedIds = []
  elements.value.forEach(element => {
    // Check if element overlaps with selection area
    const elementLeft = element.x
    const elementRight = element.x + element.width
    const elementTop = element.y
    const elementBottom = element.y + element.height

    // Check for overlap (element intersects with selection area)
    if (!(elementRight < minX || elementLeft > maxX ||
          elementBottom < minY || elementTop > maxY)) {
      selectedIds.push(element.id)
    }
  })

  selectedElements.value = selectedIds
  updateColorControls()

  console.log('Selection completed:', selectedIds.length, 'elements selected')
  console.log('Selected element IDs:', selectedIds)
  console.log('Selection area:', { minX, maxX, minY, maxY })
}

// Element movement and resizing
const moveElement = (primaryElementId, event) => {
  const primaryElement = getElementById(primaryElementId)
  if (!primaryElement || primaryElement.locked) return

  const rect = canvas.value.getBoundingClientRect()
  const newX = event.clientX - rect.left - dragOffset.value.x
  const newY = event.clientY - rect.top - dragOffset.value.y

  // Calculate delta movement
  const deltaX = newX - primaryElement.x
  const deltaY = newY - primaryElement.y

  // Move all selected elements by the same delta
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element && !element.locked) {
      element.x += deltaX
      element.y += deltaY
    }
  })
}

const resizeElement = (event) => {
  const { elementId, handle, startX, startY, startWidth, startHeight } = resizeData.value
  const element = getElementById(elementId)
  if (!element) return

  const rect = canvas.value.getBoundingClientRect()
  const currentX = event.clientX - rect.left
  const currentY = event.clientY - rect.top
  
  // Account for zoom level in delta calculations
  const deltaX = (currentX - startX) / zoomLevel.value
  const deltaY = (currentY - startY) / zoomLevel.value

  switch (handle) {
    case 'se': // Southeast
      element.width = Math.max(10, startWidth + deltaX)
      element.height = Math.max(10, startHeight + deltaY)
      break
    case 'sw': // Southwest
      const newWidthSW = Math.max(10, startWidth - deltaX)
      element.width = newWidthSW
      element.height = Math.max(10, startHeight + deltaY)
      element.x = resizeData.value.startElementX + (startWidth - newWidthSW)
      break
    case 'ne': // Northeast
      element.width = Math.max(10, startWidth + deltaX)
      const newHeightNE = Math.max(10, startHeight - deltaY)
      element.height = newHeightNE
      element.y = resizeData.value.startElementY + (startHeight - newHeightNE)
      break
    case 'nw': // Northwest
      const newWidthNW = Math.max(10, startWidth - deltaX)
      const newHeightNW = Math.max(10, startHeight - deltaY)
      element.width = newWidthNW
      element.height = newHeightNW
      element.x = resizeData.value.startElementX + (startWidth - newWidthNW)
      element.y = resizeData.value.startElementY + (startHeight - newHeightNW)
      break
  }
}

// Actions
const duplicateSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  const newElements = []
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      const newElement = { ...element, id: generateId(), x: element.x + 20, y: element.y + 20 }
      newElements.push(newElement)
      elements.value.push(newElement)
    }
  })

  selectedElements.value = newElements.map(el => el.id)
}

const rotateSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      element.rotation = (element.rotation || 0) + 90
      if (element.rotation >= 360) element.rotation = 0
    }
  })
}

const updateSelectedColors = () => {
  if (selectedElements.value.length === 0) return
  saveState()

  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      // Update fill color for shapes and icons
      if (element.type === 'shape' || element.type === 'icon') {
        element.color = selectedFillColor.value
      }
      // Update text color for text elements
      if (element.type === 'text') {
        element.color = selectedTextColor.value
      }
      // Update border color for shapes and images
      if (element.type === 'shape' || element.type === 'image') {
        element.borderColor = selectedBorderColor.value
      }
    }
  })
}

const deleteSelected = () => {
  if (selectedElements.value.length === 0) return
  saveState()
  
  elements.value = elements.value.filter(el => !selectedElements.value.includes(el.id))
  selectedElements.value = []
}

const clearAll = () => {
  if (elements.value.length === 0) return
  saveState()
  elements.value = []
  selectedElements.value = []
}

// History
const undo = () => {
  if (!canUndo.value) return
  historyIndex.value--
  elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
  selectedElements.value = []
}

const redo = () => {
  if (!canRedo.value) return
  historyIndex.value++
  elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
  selectedElements.value = []
}

// Project management
const newProject = () => {
  if (elements.value.length > 0) {
    if (!confirm('创建新项目将清除所有元素，确定要继续吗？')) {
      return
    }
  }
  saveState()
  elements.value = []
  selectedElements.value = []
  canvasBackground.value = '#ffffff'
  zoomLevel.value = 1
  showGrid.value = false
}

const importProject = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (event) => {
    const file = event.target.files[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const project = JSON.parse(e.target.result)
        saveState()
        elements.value = project.elements || []
        canvasBackground.value = project.canvasBackground || '#ffffff'
        selectedElements.value = []
      } catch (error) {
        alert('导入项目文件出错，请检查文件格式')
      }
    }
    reader.readAsText(file)
  }
  input.click()
}

const saveProject = () => {
  const project = {
    elements: elements.value,
    canvasBackground: canvasBackground.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(project, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `visual-editor-project-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const exportAsJSON = () => {
  saveProject()
}

const triggerLoadProject = () => {
  fileInput.value?.click()
}

const loadProject = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const project = JSON.parse(e.target.result)
      saveState()
      elements.value = project.elements || []
      canvasBackground.value = project.canvasBackground || '#ffffff'
      selectedElements.value = []
    } catch (error) {
      alert('Error loading project file')
    }
  }
  reader.readAsText(file)
}

// Styling functions
const getElementStyle = (element) => {
  const baseStyle = {
    position: 'absolute',
    left: element.x + 'px',
    top: element.y + 'px',
    width: element.width + 'px',
    height: element.height + 'px',
    color: element.color,
    fontSize: element.fontSize ? element.fontSize + 'px' : Math.max(12, Math.min(element.height * 0.6, 48)) + 'px',
    fontWeight: element.fontWeight || 'normal',
    fontStyle: element.fontStyle || 'normal',
    textDecoration: element.textDecoration || 'none',
    fontFamily: element.fontFamily || 'Arial, sans-serif',
    cursor: element.locked ? 'not-allowed' : 'move',
    userSelect: 'none',
    transform: `rotate(${element.rotation || 0}deg)`,
    transformOrigin: 'center center',
    opacity: element.opacity !== undefined ? element.opacity : 1,
    display: element.hidden ? 'none' : 'block'
  }

  // Add shadow effect if enabled
  if (element.hasShadow) {
    baseStyle.boxShadow = `${element.shadowOffsetX || 2}px ${element.shadowOffsetY || 2}px ${element.shadowBlur || 4}px ${element.shadowColor || '#000000'}`
  }

  return baseStyle
}

const getCanvasStyle = () => {
  const gridPattern = 'data:image/svg+xml,' + encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
      <rect width="20" height="20" fill="none" stroke="#e0e0e0" stroke-width="0.5"/>
    </svg>
  `)
  
  return {
    background: showGrid.value ? `${canvasBackground.value} url('${gridPattern}')` : canvasBackground.value,
    transform: `scale(${zoomLevel.value})`,
    transformOrigin: 'top left'
  }
}

const getShapeStyle = (element) => {
  const baseStyle = {
    width: '100%',
    height: '100%',
    backgroundColor: element.color,
    border: `${element.borderWidth || 2}px solid ${element.borderColor || element.color}`
  }

  switch (element.subtype) {
    case 'rectangle':
    case 'square':
      return baseStyle
    case 'circle':
      return { ...baseStyle, borderRadius: '50%' }
    case 'oval':
      return { ...baseStyle, borderRadius: '50%' }
    case 'rounded-rect':
      return { ...baseStyle, borderRadius: '15px' }
    case 'triangle':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'diamond':
      return { ...baseStyle, transform: 'rotate(45deg)' }
    case 'line':
      return {
        width: '100%',
        height: `${element.borderWidth || 2}px`,
        backgroundColor: element.borderColor || '#000000',
        border: 'none'
      }
    case 'arrow':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'star':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: `${Math.min(element.width, element.height) * 0.8}px`,
        color: element.color || '#ffffff',
        border: 'none'
      }
    case 'heart':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: `${Math.min(element.width, element.height) * 0.8}px`,
        color: element.color || '#ffffff',
        border: 'none'
      }


    case 'trapezoid':
    case 'hexagon':
    case 'octagon':
    case 'parallelogram':
    case 'cross':
    case 'polygon':
    case 'curve':
    case 'dotted-line':
    case 'dotted-curve':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        border: 'none',
        position: 'relative'
      }
    case 'cross':
      return {
        width: '100%',
        height: '100%',
        backgroundColor: element.color || '#ffffff',
        border: `${element.borderWidth || 2}px solid ${element.borderColor || '#000000'}`,
        clipPath: 'polygon(40% 0%, 60% 0%, 60% 40%, 100% 40%, 100% 60%, 60% 60%, 60% 100%, 40% 100%, 40% 60%, 0% 60%, 0% 40%, 40% 40%)'
      }
    default:
      return baseStyle
  }
}

const getIconStyle = (element) => {
  return {
    fontSize: Math.min(element.width, element.height) + 'px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%'
  }
}

const getImageStyle = (element) => {
  return {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    border: element.borderWidth ? `${element.borderWidth}px solid ${element.borderColor}` : 'none',
    borderRadius: '4px'
  }
}

const getCustomSvgStyle = (element) => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
}

const getFreehandDrawingStyle = (element) => {
  return {
    width: '100%',
    height: '100%'
  }
}

const getMathShapeStyle = (element) => {
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: (element.fontSize || 24) + 'px',
    fontWeight: element.fontWeight || 'bold',
    color: element.color || '#000000',
    fontFamily: 'Arial, sans-serif'
  }
}

// Event handlers
const updateMousePosition = (event) => {
  const rect = canvas.value.getBoundingClientRect()
  mousePosition.value = {
    x: Math.round(event.clientX - rect.left),
    y: Math.round(event.clientY - rect.top)
  }
}

const handleMouseMove = (event) => {
  updateMousePosition(event)

  if (isDragging.value && draggedElement.value) {
    const rect = canvas.value.getBoundingClientRect()
    const newX = Math.max(0, event.clientX - rect.left - dragOffset.value.x)
    const newY = Math.max(0, event.clientY - rect.top - dragOffset.value.y)

    // Get the primary dragged element
    const primaryElement = getElementById(draggedElement.value)
    if (primaryElement) {
      const deltaX = newX - primaryElement.x
      const deltaY = newY - primaryElement.y

      // Move all selected elements by the same delta
      selectedElements.value.forEach(id => {
        const element = getElementById(id)
        if (element) {
          element.x = Math.max(0, element.x + deltaX)
          element.y = Math.max(0, element.y + deltaY)
        }
      })

      console.log('Dragging', selectedElements.value.length, 'elements by delta:', deltaX, deltaY)
    }
  } else if (isResizing.value && resizeData.value.elementId) {
    const element = getElementById(resizeData.value.elementId)
    if (element) {
      const rect = canvas.value.getBoundingClientRect()
      const currentX = event.clientX - rect.left
      const currentY = event.clientY - rect.top
      const deltaX = currentX - resizeData.value.startX
      const deltaY = currentY - resizeData.value.startY

      const handle = resizeData.value.handle

      if (handle.includes('e')) { // East (right)
        element.width = Math.max(20, resizeData.value.startWidth + deltaX)
      }
      if (handle.includes('w')) { // West (left)
        const newWidth = Math.max(20, resizeData.value.startWidth - deltaX)
        element.x = resizeData.value.startElementX + (resizeData.value.startWidth - newWidth)
        element.width = newWidth
      }
      if (handle.includes('s')) { // South (bottom)
        element.height = Math.max(20, resizeData.value.startHeight + deltaY)
      }
      if (handle.includes('n')) { // North (top)
        const newHeight = Math.max(20, resizeData.value.startHeight - deltaY)
        element.y = resizeData.value.startElementY + (resizeData.value.startHeight - newHeight)
        element.height = newHeight
      }
    }
  } else if (isRotating.value && rotateData.value.elementId) {
    const element = getElementById(rotateData.value.elementId)
    if (element) {
      const rect = canvas.value.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      const currentAngle = Math.atan2(mouseY - rotateData.value.centerY, mouseX - rotateData.value.centerX) * 180 / Math.PI
      let rotation = currentAngle - rotateData.value.startAngle

      // Normalize rotation to 0-359 degrees
      while (rotation < 0) rotation += 360
      while (rotation >= 360) rotation -= 360

      element.rotation = Math.round(rotation)
    }
  } else if (isSelecting.value) {
    // Update selection area
    const rect = canvas.value.getBoundingClientRect()
    selectionArea.value.endX = event.clientX - rect.left
    selectionArea.value.endY = event.clientY - rect.top
  }
}

const handleMouseUp = (event) => {
  if (isDragging.value || isResizing.value || isRotating.value) {
    saveState()
  }

  // Handle selection completion
  if (isSelecting.value) {
    console.log('Completing selection...')
    completeSelection()
  }

  isDragging.value = false
  isResizing.value = false
  isRotating.value = false
  isSelecting.value = false
  draggedElement.value = null
  resizeData.value = { elementId: null, handle: null, startX: 0, startY: 0, startWidth: 0, startHeight: 0 }
  rotateData.value = { elementId: null, startAngle: 0, centerX: 0, centerY: 0 }
  selectionArea.value = { active: false, startX: 0, startY: 0, endX: 0, endY: 0 }
}

const handleKeyDown = (event) => {
  // Delete/Backspace - Delete selected elements
  if ((event.key === 'Delete' || event.key === 'Backspace') && selectedElements.value.length > 0) {
    event.preventDefault()
    deleteSelected()
  }
  // Escape - Clear selection
  else if (event.key === 'Escape') {
    selectedElements.value = []
    hideContextMenu()
  }
  // Ctrl+Z - Undo
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'z' && !event.shiftKey) {
    event.preventDefault()
    undo()
  }
  // Ctrl+Shift+Z or Ctrl+Y - Redo
  else if ((event.ctrlKey || event.metaKey) && (event.shiftKey && event.key.toLowerCase() === 'z' || event.key.toLowerCase() === 'y')) {
    event.preventDefault()
    redo()
  }
  // Ctrl+C - Copy
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c' && selectedElements.value.length > 0) {
    event.preventDefault()
    copySelected()
  }
  // Ctrl+V - Paste
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'v') {
    event.preventDefault()
    pasteSelected()
  }
  // Ctrl+X - Cut
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'x' && selectedElements.value.length > 0) {
    event.preventDefault()
    cutSelected()
  }
  // Ctrl+D - Duplicate
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'd') {
    event.preventDefault()
    duplicateSelected()
  }
  // Ctrl+A - Select All
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'a') {
    event.preventDefault()
    selectAll()
  }
  // Ctrl+S - Save Project
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 's') {
    event.preventDefault()
    saveProject()
  }
  // Ctrl+G - Group elements
  else if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'g' && !event.shiftKey) {
    event.preventDefault()
    groupElements()
  }
  // Ctrl+Shift+G - Ungroup elements
  else if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key.toLowerCase() === 'g') {
    event.preventDefault()
    ungroupElements()
  }
  // R - Rotate selected elements
  else if (event.key.toLowerCase() === 'r' && selectedElements.value.length > 0) {
    event.preventDefault()
    rotateSelected()
  }
  // G - Toggle grid
  else if (event.key.toLowerCase() === 'g' && !event.ctrlKey && !event.metaKey) {
    event.preventDefault()
    toggleGrid()
  }
  // + - Zoom in
  else if (event.key === '+' || event.key === '=') {
    event.preventDefault()
    zoomIn()
  }
  // - - Zoom out
  else if (event.key === '-') {
    event.preventDefault()
    zoomOut()
  }
  // 0 - Reset zoom
  else if (event.key === '0') {
    event.preventDefault()
    resetZoom()
  }
  // Arrow Keys - Move selected elements (但不在编辑模式下)
  else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key) && selectedElements.value.length > 0 && !isAnyElementEditing()) {
    event.preventDefault()
    moveSelectedElements(event.key, event.shiftKey ? 10 : 1)
  }
}

const startDragging = (id, event) => {
  // Handle multi-select with Ctrl+click first
  if (event.ctrlKey || event.metaKey) {
    event.preventDefault()
    event.stopPropagation()
    // Handle multi-select
    if (selectedElements.value.includes(id)) {
      selectedElements.value = selectedElements.value.filter(el => el !== id)
    } else {
      selectedElements.value.push(id)
    }
    updateColorControls()
    return // Don't start dragging on Ctrl+click
  }

  // Regular single-select and drag
  event.preventDefault()
  event.stopPropagation()

  // Select the element if not already selected
  if (!selectedElements.value.includes(id)) {
    selectedElements.value = [id]
    updateColorControls()
  }

  isDragging.value = true
  draggedElement.value = id
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    dragOffset.value = {
      x: event.clientX - rect.left - element.x,
      y: event.clientY - rect.top - element.y
    }
  }
  hideContextMenu()
  console.log('Started dragging element:', id)
}

const startResizing = (id, handle, event) => {
  event.preventDefault()
  event.stopPropagation()
  isResizing.value = true
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    resizeData.value = {
      elementId: id,
      handle: handle,
      startX: event.clientX - rect.left,
      startY: event.clientY - rect.top,
      startWidth: element.width,
      startHeight: element.height,
      startElementX: element.x,
      startElementY: element.y
    }
  }
  hideContextMenu()
}

const startRotating = (id, event) => {
  event.preventDefault()
  event.stopPropagation()
  isRotating.value = true
  const element = getElementById(id)
  if (element) {
    const rect = canvas.value.getBoundingClientRect()
    const centerX = element.x + element.width / 2
    const centerY = element.y + element.height / 2
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * 180 / Math.PI

    rotateData.value = {
      elementId: id,
      startAngle: startAngle - (element.rotation || 0),
      centerX: centerX,
      centerY: centerY
    }
  }
  hideContextMenu()
}

const updateElement = (id) => {
  // Force reactivity update
  const element = getElementById(id)
  if (element) {
    // Trigger reactivity
    elements.value = [...elements.value]
  }
}

// Text editing functions
const startEditing = (id) => {
  const element = getElementById(id)
  if (element && (element.type === 'text' || element.type === 'label' || element.type === 'title')) {
    element.isEditing = true
    // Focus the element after Vue updates
    setTimeout(() => {
      const elementDiv = document.querySelector(`[data-element-id="${id}"]`)
      if (elementDiv) {
        elementDiv.focus()
        // Select all text for easy editing
        const range = document.createRange()
        range.selectNodeContents(elementDiv)
        const selection = window.getSelection()
        selection.removeAllRanges()
        selection.addRange(range)
        
        // Add editing class for visual feedback
        elementDiv.classList.add('text-editing')
      }
    }, 10)
  }
}

const stopEditing = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.isEditing = false
    if (event && event.target) {
      element.content = event.target.textContent || element.content
      // Remove editing class
      event.target.classList.remove('text-editing')
    }
  }
}

const updateElementText = (id, event) => {
  const element = getElementById(id)
  if (element) {
    element.content = event.target.textContent
  }
}

// Helper function to check if element is text-based
const isTextElement = (id) => {
  const element = getElementById(id)
  return element && (element.type === 'text' || element.type === 'label' || element.type === 'title')
}

// Helper function to check if any element is currently being edited
const isAnyElementEditing = () => {
  return elements.value.some(element => element.isEditing === true)
}

// Context menu edit function
const editFromContext = () => {
  if (contextMenu.value.elementId) {
    startEditing(contextMenu.value.elementId)
  }
  hideContextMenu()
}

// Context Menu
const hideContextMenu = () => {
  contextMenu.value.visible = false
  contextMenu.value.elementId = null
}

const handleCanvasContextMenu = (event) => {
  event.preventDefault()
  hideContextMenu()
}

const handleElementContextMenu = (id, event) => {
  event.preventDefault()
  event.stopPropagation()

  if (!selectedElements.value.includes(id)) {
    selectedElements.value = [id]
  }

  const rect = canvas.value.getBoundingClientRect()
  contextMenu.value = {
    visible: true,
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
    elementId: id
  }
}

const duplicateFromContext = () => {
  duplicateSelected()
  hideContextMenu()
}

const copyFromContext = () => {
  if (selectedElements.value.length > 0) {
    clipboard.value = selectedElements.value.map(id => {
      const element = getElementById(id)
      return element ? { ...element } : null
    }).filter(Boolean)
  }
  hideContextMenu()
}

const pasteFromContext = () => {
  if (clipboard.value && clipboard.value.length > 0) {
    saveState()
    const newElements = []
    clipboard.value.forEach(elementData => {
      const newElement = {
        ...elementData,
        id: generateId(),
        x: elementData.x + 20,
        y: elementData.y + 20
      }
      newElements.push(newElement)
      elements.value.push(newElement)
    })
    selectedElements.value = newElements.map(el => el.id)
  }
  hideContextMenu()
}

const deleteFromContext = () => {
  deleteSelected()
  hideContextMenu()
}

// Additional shortcut functions
const copySelected = () => {
  if (selectedElements.value.length > 0) {
    clipboard.value = selectedElements.value.map(id => {
      const element = getElementById(id)
      return element ? { ...element } : null
    }).filter(Boolean)
  }
}

const pasteSelected = () => {
  if (clipboard.value && clipboard.value.length > 0) {
    saveState()
    const newElements = []
    clipboard.value.forEach(elementData => {
      const newElement = {
        ...elementData,
        id: generateId(),
        x: elementData.x + 20,
        y: elementData.y + 20
      }
      newElements.push(newElement)
      elements.value.push(newElement)
    })
    selectedElements.value = newElements.map(el => el.id)
  }
}

const cutSelected = () => {
  if (selectedElements.value.length > 0) {
    copySelected()
    deleteSelected()
  }
}

const selectAll = () => {
  selectedElements.value = elements.value.map(el => el.id)
}

const moveSelectedElements = (direction, distance) => {
  if (selectedElements.value.length === 0) return

  saveState()
  selectedElements.value.forEach(id => {
    const element = getElementById(id)
    if (element) {
      switch (direction) {
        case 'ArrowUp':
          element.y = Math.max(0, element.y - distance)
          break
        case 'ArrowDown':
          element.y = element.y + distance
          break
        case 'ArrowLeft':
          element.x = Math.max(0, element.x - distance)
          break
        case 'ArrowRight':
          element.x = element.x + distance
          break
      }
    }
  })
}

// Global mouse events
const globalMouseMove = (event) => {
  if (isSelecting.value) {
    const rect = canvas.value.getBoundingClientRect()
    selectionArea.value.endX = event.clientX - rect.left
    selectionArea.value.endY = event.clientY - rect.top
    
    // Update selection in real time
    const minX = Math.min(selectionArea.value.startX, selectionArea.value.endX)
    const maxX = Math.max(selectionArea.value.startX, selectionArea.value.endX)
    const minY = Math.min(selectionArea.value.startY, selectionArea.value.endY)
    const maxY = Math.max(selectionArea.value.startY, selectionArea.value.endY)
    
    const selectedIds = []
    elements.value.forEach(element => {
      if (element.x >= minX && element.x + element.width <= maxX &&
          element.y >= minY && element.y + element.height <= maxY) {
        selectedIds.push(element.id)
      }
    })
    
    selectedElements.value = selectedIds
  }
  
  if (isDragging.value && draggedElement.value) {
    moveElement(draggedElement.value, event)
  }
  
  if (isResizing.value && resizeData.value.elementId) {
    resizeElement(event)
  }
  
  if (isRotating.value && rotateData.value.elementId) {
    const rect = canvas.value.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top
    
    const angle = Math.atan2(
      mouseY - rotateData.value.centerY,
      mouseX - rotateData.value.centerX
    ) * 180 / Math.PI
    
    const element = getElementById(rotateData.value.elementId)
    if (element) {
      element.rotation = (angle - rotateData.value.startAngle + 360) % 360
    }
  }
  }

const globalMouseUp = () => {
  if (isSelecting.value) {
    completeSelection()
    // 延迟隐藏选择框，让用户能看到选择结果
    setTimeout(() => {
      isSelecting.value = false
      selectionArea.value.active = false
    }, 100)
  }
  
  if (isDragging.value) {
    isDragging.value = false
    draggedElement.value = null
  }
  
  if (isResizing.value) {
    isResizing.value = false
    resizeData.value = { elementId: null, handle: null, startX: 0, startY: 0, startWidth: 0, startHeight: 0 }
  }
  
  if (isRotating.value) {
    isRotating.value = false
    rotateData.value = { elementId: null, startAngle: 0, centerX: 0, centerY: 0 }
  }
}

// Initialize
onMounted(() => {
  saveState()
  canvas.value?.focus()
  
  // Add global event listeners
  document.addEventListener('mousemove', globalMouseMove)
  document.addEventListener('mouseup', globalMouseUp)
})

// Cleanup on unmount
onUnmounted(() => {
  document.removeEventListener('mousemove', globalMouseMove)
  document.removeEventListener('mouseup', globalMouseUp)
})
</script>

<style scoped>
.jeecg-visual-editor {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;
}

.editor-header {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  padding: 16px 24px;
  text-align: center;
  border-radius: 6px 6px 0 0;
}

.editor-header h2 {
  margin: 0 0 8px 0;
  font-size: 1.5em;
  font-weight: 600;
}

.editor-header p {
  margin: 0;
  font-size: 0.9em;
  opacity: 0.9;
}

/* Microsoft Office Ribbon工具栏样式 */
.ribbon-toolbar {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  padding: 8px 16px;
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  min-height: 120px;
  overflow-x: auto;
}

/* 全局按钮重置 - 确保所有ribbon按钮统一 */
.ribbon-toolbar .ant-btn,
.ribbon-toolbar button {
  box-sizing: border-box !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.ribbon-group {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* 数学符号组特殊宽度 */
.ribbon-group:has(.ribbon-math-symbols-compact) {
  min-width: 180px;
}

.ribbon-group-header {
  background: #e9ecef;
  color: #495057;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  padding: 6px 8px;
  border-bottom: 1px solid #dee2e6;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.ribbon-group-content {
  padding: 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: hidden;
  box-sizing: border-box;
}

.ribbon-button-group {
  display: flex;
  gap: 3px;
  align-items: stretch;
  flex-wrap: wrap;
  justify-content: flex-start;
  box-sizing: border-box;
  padding: 2px;
}

.ribbon-button-group .ant-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 8px;
  height: auto;
  min-height: 50px;
  justify-content: center;
  border-radius: 4px;
  border: 1px solid transparent;
  font-size: 12px;
  flex-shrink: 0;
}

/* 统一按钮样式 */
.icon-only-btn {
  min-width: 32px !important;
  min-height: 32px !important;
  max-width: 32px !important;
  max-height: 32px !important;
  padding: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  background: white !important;
  margin: 1px !important;
  flex-shrink: 0 !important;
}

.icon-only-btn svg {
  width: 16px !important;
  height: 16px !important;
}

/* 下拉按钮特殊处理 */
.icon-only-btn svg + svg {
  width: 12px !important;
  height: 12px !important;
  margin-left: 2px;
}

.ribbon-button-group .ant-btn:hover,
.icon-only-btn:hover {
  border-color: #1890ff !important;
  background: #f0f8ff !important;
  transform: none !important;
}

/* 原button-text样式已移除，现在使用图标模式 */

/* 形状和图标网格 */
.ribbon-shapes-grid,
.ribbon-icons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3px;
  max-width: 108px;
  padding: 2px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 数学符号紧凑布局 */
.ribbon-math-symbols-compact {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 170px;
  padding: 2px;
}

.math-row {
  display: flex;
  gap: 3px;
  justify-content: flex-start;
}

.shape-btn-small {
  width: 30px !important;
  height: 30px !important;
  min-width: 30px !important;
  min-height: 30px !important;
  max-width: 30px !important;
  max-height: 30px !important;
  padding: 2px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  margin: 1px;
  flex-shrink: 0;
}

.shape-btn-small:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: none;
}

/* 根号按钮特殊样式 */
.math-sqrt-btn svg {
  pointer-events: none;
}

/* 数学符号特殊字体 */
.math-shape-content span {
  font-family: 'Times New Roman', 'Arial Unicode MS', 'Segoe UI Symbol', serif;
  font-size: 1.2em;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}

/* 数学符号文本可缩放样式 */
.math-symbol-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-weight: bold;
  text-align: center;
}

/* 数学符号网格 */
.ribbon-math-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
  margin-top: 4px;
}

.math-btn {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.math-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: scale(1.1);
}

/* 工具栏中的数学符号按钮 */
.math-btn {
  font-family: 'Cambria Math', 'Times New Roman', 'Arial Unicode MS', 'Segoe UI Symbol', serif;
  font-size: 16px;
  text-rendering: optimizeLegibility;
}

/* 文本格式按钮组 */
.ribbon-format-group {
  display: flex;
  gap: 2px;
  margin-top: 4px;
}

.ribbon-format-group .ant-btn {
  min-width: 30px;
  padding: 4px 8px;
  min-height: 28px;
}

/* 属性面板数学符号样式 */
.math-symbols-panel {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
  margin-top: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.math-symbol-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  font-family: 'Cambria Math', 'Times New Roman', 'Arial Unicode MS', 'Segoe UI Symbol', serif;
  text-rendering: optimizeLegibility;
}

.math-symbol-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: scale(1.1);
}

/* 根号特殊样式 */
.sqrt-btn {
  position: relative;
  overflow: visible;
}

.sqrt-symbol {
  font-family: 'Cambria Math', 'Times New Roman', 'Arial Unicode MS', 'Segoe UI Symbol', serif;
  font-size: 22px;
  font-weight: normal;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
  color: #262626;
  text-rendering: optimizeLegibility;
  -webkit-font-feature-settings: "liga" on;
  font-feature-settings: "liga" on;
}

/* 文本格式控制样式 */
.text-format-controls {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.format-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.format-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.format-btn.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.shape-btn,
.icon-btn {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  max-width: 32px !important;
  max-height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  margin: 1px;
  padding: 2px !important;
  flex-shrink: 0;
}

.shape-btn:hover,
.icon-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: none;
}

.shape-btn svg {
  pointer-events: none;
}

/* 数学形状样式 */
.math-shape-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

/* 长除法符号样式 */
.division-bracket-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 紧凑布局样式 */
.ribbon-compact-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.compact-row {
  display: flex;
  gap: 2px;
  justify-content: flex-start;
  align-items: center;
}

.compact-btn {
  width: 30px !important;
  height: 30px !important;
  min-width: 30px !important;
  min-height: 30px !important;
  max-width: 30px !important;
  max-height: 30px !important;
  padding: 2px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  margin: 1px;
  flex-shrink: 0;
}

.compact-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: none;
}

.compact-select {
  height: 30px !important;
  margin: 1px;
  flex-shrink: 0;
}

/* 手绘工具水平布局 */
.ribbon-button-group-horizontal {
  display: flex;
  flex-wrap: nowrap;
  gap: 2px;
  padding: 4px;
  overflow-x: auto;
  max-width: 100%;
}

.ribbon-button-group-horizontal .shape-btn {
  flex-shrink: 0;
  min-width: 32px;
}

/* 导出下拉菜单样式 */
.ant-dropdown-menu {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  border: 1px solid #e6f7ff;
}

.ant-dropdown-menu-item {
  padding: 8px 16px;
  font-size: 13px;
}

.ant-dropdown-menu-item:hover {
  background: #f0f8ff;
  color: #1890ff;
}

.editor-main {
  display: flex;
  flex: 1;
  height: calc(100vh - 180px);
  overflow: hidden;
  border: none;
}

.toolbar h3 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.tool-section {
  margin-bottom: 16px;
}

.tool-section h4 {
  margin: 0 0 8px 0;
  color: #595959;
  font-size: 13px;
  font-weight: 500;
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tool-btn {
  justify-content: flex-start;
}

.shape-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
}

.shape-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.url-input-section {
  display: flex;
  gap: 6px;
  margin-top: 6px;
}

.url-add-btn {
  flex-shrink: 0;
}

/* 画布区域 */
.canvas-container {
  flex: 1;
  background: #fafafa;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.canvas {
  width: 90%;
  height: 90%;
  background: white;
  position: relative;
  outline: none;
  cursor: crosshair;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  overflow: hidden;
}

/* 画布元素样式 */
.canvas-element {
  position: absolute;
  border: 2px solid transparent;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.canvas-element:hover {
  outline: 1px dashed #1890ff;
}

.canvas-element.element-selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 1px #1890ff;
}

.canvas-element.element-selected:hover {
  outline: none;
}

.canvas-element.element-hidden {
  display: none;
}

.canvas-element.element-locked {
  cursor: not-allowed;
}

.text-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  text-align: inherit;
  word-wrap: break-word;
  overflow: hidden;
}

.text-content[contenteditable="true"] {
  outline: 1px dashed #1890ff;
  background: rgba(24, 144, 255, 0.05);
  cursor: text;
}

.shape-content,
.icon-content,
.image-content {
  width: 100%;
  height: 100%;
}

/* 调整手柄样式 */
.resize-handles {
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #1890ff;
  border: 2px solid white;
  border-radius: 2px;
  pointer-events: all;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.resize-handle:hover {
  background: #0050b3;
  transform: scale(1.2);
}

.resize-handle.nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.rotate-handle {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 24px;
  background: #52c41a;
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  pointer-events: all;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.rotate-handle:hover {
  background: #389e0d;
  transform: translateX(-50%) scale(1.1);
}

.rotate-handle:active {
  cursor: grabbing;
}

.mouse-position {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

/* 右侧属性面板 */
.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #f0f0f0;
  overflow-y: auto;
  flex-shrink: 0;
}

.properties-card {
  height: 100%;
  border: none;
  border-radius: 0;
  padding: 16px;
  overflow-y: auto;
}

.properties-panel h3 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.property-section {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.property-section h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.property-item {
  margin-bottom: 12px;
}

.property-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

.color-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker {
  width: 40px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: none;
}

.color-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* Icon Library Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: #34495e;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content.small {
  width: 400px;
  max-width: 90vw;
  background: white;
  color: #333;
}

.modal-content.small .modal-header {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  color: #495057;
}

.modal-content.small .modal-header h3 {
  color: #495057;
}

.modal-content.small .modal-close {
  color: #6c757d;
}

.modal-content.small .modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

/* 图片URL对话框样式 */
.url-input-section {
  margin-bottom: 20px;
}

.url-input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.url-input-full {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.url-input-full:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  color: #595959;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.btn.primary {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn.primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.btn:disabled:hover {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2c3e50;
  background: #2c3e50;
}

.modal-header h3 {
  color: #ecf0f1;
  margin: 0;
  font-size: 18px;
}

.modal-close {
  background: none;
  border: none;
  color: #bdc3c7;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.modal-close:hover {
  background: #34495e;
  color: #ecf0f1;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 70vh;
}

.icon-categories {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  background: #2c3e50;
  color: #bdc3c7;
  border: 1px solid #34495e;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 12px;
}

.category-btn:hover {
  background: #34495e;
  color: #ecf0f1;
}

.category-btn.active {
  background: #3498db;
  color: white;
  border-color: #2980b9;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 10px;
  margin-bottom: 30px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #2c3e50;
  border-radius: 4px;
  background: #2c3e50;
}

.icon-item {
  width: 60px;
  height: 60px;
  background: #34495e;
  border: 1px solid #2c3e50;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s;
}

.icon-item:hover {
  background: #3498db;
  border-color: #2980b9;
  transform: scale(1.05);
}

.custom-svg-section {
  border-top: 1px solid #2c3e50;
  padding-top: 20px;
}

.custom-svg-section h4 {
  color: #ecf0f1;
  margin-bottom: 15px;
  font-size: 16px;
}

.svg-textarea {
  width: 100%;
  height: 120px;
  background: #2c3e50;
  border: 1px solid #34495e;
  border-radius: 4px;
  color: white;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  resize: vertical;
  margin-bottom: 15px;
}

.svg-textarea:focus {
  outline: none;
  border-color: #3498db;
}

.svg-textarea::placeholder {
  color: #95a5a6;
}

/* Font Controls */
.font-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #2c3e50;
  border-radius: 4px;
  background: #2c3e50;
  color: white;
  font-size: 12px;
}

.font-select:focus {
  outline: none;
  border-color: #3498db;
  background: #34495e;
}

.unit-label {
  font-size: 12px;
  color: #95a5a6;
  margin-left: 5px;
}

.opacity-slider {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 5px;
  background: #2c3e50;
  outline: none;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.range-input {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 5px;
  background: #2c3e50;
  outline: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.range-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
}

.shadow-controls {
  margin-top: 10px;
  padding-left: 20px;
}

.ribbon-btn.active {
  background: #3498db;
  color: white;
  border-color: #2980b9;
}

.property-item input[type="color"] {
  width: 100%;
  height: 30px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  cursor: pointer;
}

.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 5px;
}

/* 选择区域 */
.selection-area {
  position: absolute;
  border: 2px dashed #1890ff;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 999;
  border-radius: 2px;
}

/* 右键菜单 */
.context-menu {
  position: absolute;
  z-index: 1000;
}

.context-menu-content {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #f0f0f0;
  min-width: 120px;
}

.danger-item {
  color: #ff4d4f !important;
}

/* Improved element selection */
.element {
  transition: none;
}

.element:hover {
  outline: 1px dashed #3498db;
}

.element.selected:hover {
  outline: none;
}

/* Better resize handles */
.resize-handle {
  border-radius: 1px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.resize-handle:hover {
  background: #2980b9;
  transform: scale(1.2);
}

.rotate-handle {
  position: absolute;
  width: 26px;
  height: 26px;
  background: #52c41a;
  border: 2px solid white;
  border-radius: 50%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  z-index: 1002;
  pointer-events: auto;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  transition: all 0.2s ease;
}

.rotate-top {
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
}

.rotate-top:hover {
  background: #389e0d;
  transform: translateX(-50%) scale(1.15);
  box-shadow: 0 3px 8px rgba(0,0,0,0.4);
}

.rotate-handle:active {
  cursor: grabbing;
}

/* Selection Area */
.selection-area {
  position: absolute;
  border: 2px dashed #3498db;
  background: rgba(52, 152, 219, 0.1);
  pointer-events: none;
  z-index: 999;
}

/* 文本编辑状态样式 */
.text-editing {
  outline: 2px solid #1890ff !important;
  outline-offset: 2px;
  background: rgba(24, 144, 255, 0.05) !important;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1) !important;
  border-radius: 4px;
}

/* 编辑状态下隐藏resize handles */
.text-editing + .resize-handles {
  display: none !important;
}


</style>
